// JavaScript para la página de letras - MusicApp

document.addEventListener('DOMContentLoaded', function() {
    // Elementos de la interfaz
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    const lyricsText = document.getElementById('lyrics-text');
    const lyricsPreview = document.querySelector('.preview-content');
    const lyricsTitle = document.getElementById('lyrics-title');
    const lyricsArtist = document.getElementById('lyrics-artist');
    const lyricsGenre = document.getElementById('lyrics-genre');
    const lyricsStatus = document.getElementById('lyrics-status');
    const previewButton = document.querySelector('.editor-actions .btn-secondary');
    const saveButton = document.querySelector('.editor-actions .btn-primary');
    
    // Verificar si el usuario está autenticado
    const isLoggedIn = MusicApp.getFromLocalStorage('musicapp_user')?.isLoggedIn || false;
    
    // Mostrar/ocultar elementos según el estado de autenticación
    const loginPrompt = document.querySelector('.login-prompt');
    if (loginPrompt) {
        loginPrompt.style.display = isLoggedIn ? 'none' : 'block';
    }
    
    // Cambiar entre pestañas
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tabName = this.getAttribute('data-tab');
            
            // Desactivar todas las pestañas
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // Activar la pestaña seleccionada
            this.classList.add('active');
            document.querySelector(`.${tabName}-tab`).classList.add('active');
        });
    });
    
    // Función para actualizar la vista previa de la letra
    function updatePreview() {
        if (!lyricsText || !lyricsPreview) return;
        
        // Obtener valores
        const title = lyricsTitle?.value || 'Mi nueva canción';
        const artist = lyricsArtist?.value || 'Tu nombre';
        const genre = lyricsGenre?.options[lyricsGenre.selectedIndex]?.text || '';
        const status = lyricsStatus?.options[lyricsStatus.selectedIndex]?.text || 'Borrador';
        
        // Procesar el texto de la letra
        let content = lyricsText.value;
        
        // Reemplazar secciones [Sección]
        content = content.replace(/\[(.*?)\]/g, '<p class="section-title">[$1]</p>');
        
        // Procesar líneas
        const lines = content.split('\n');
        let processedContent = '';
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            
            if (line === '') {
                processedContent += '<br>';
                continue;
            }
            
            // Si no es una sección (ya procesada arriba)
            if (!line.startsWith('<p class="section-title">')) {
                processedContent += `<p class="lyrics-line">${line}</p>`;
            } else {
                processedContent += line;
            }
        }
        
        // Actualizar la vista previa
        lyricsPreview.innerHTML = `
            <h2>${title}</h2>
            <h3>${artist}</h3>
            <p><strong>Género:</strong> ${genre} | <strong>Estado:</strong> ${status}</p>
            ${processedContent}
        `;
    }
    
    // Event listener para la vista previa
    if (previewButton) {
        previewButton.addEventListener('click', function() {
            updatePreview();
        });
    }
    
    // Event listeners para los campos del formulario
    if (lyricsTitle) {
        lyricsTitle.addEventListener('input', updatePreview);
    }
    
    if (lyricsArtist) {
        lyricsArtist.addEventListener('input', updatePreview);
    }
    
    if (lyricsGenre) {
        lyricsGenre.addEventListener('change', updatePreview);
    }
    
    if (lyricsStatus) {
        lyricsStatus.addEventListener('change', updatePreview);
    }
    
    // Event listener para el área de texto de la letra
    if (lyricsText) {
        lyricsText.addEventListener('input', function() {
            // Actualizar la vista previa automáticamente si no es muy largo
            if (this.value.length < 1000) {
                updatePreview();
            }
        });
    }
    
    // Event listener para guardar la letra
    if (saveButton) {
        saveButton.addEventListener('click', function() {
            if (!isLoggedIn) {
                MusicApp.showAlert('Debes iniciar sesión para guardar letras.', 'error');
                return;
            }
            
            // Validar que haya contenido
            if (!lyricsText.value.trim()) {
                MusicApp.showAlert('La letra no puede estar vacía.', 'error');
                return;
            }
            
            // Validar título
            if (!lyricsTitle.value.trim()) {
                MusicApp.showAlert('Por favor, ingresa un título para la letra.', 'error');
                lyricsTitle.focus();
                return;
            }
            
            // Simular guardado
            MusicApp.simulateLoading(this, function() {
                // Crear objeto de letra
                const lyrics = {
                    id: MusicApp.generateId(),
                    title: lyricsTitle.value.trim(),
                    artist: lyricsArtist.value.trim(),
                    genre: lyricsGenre.value,
                    status: lyricsStatus.value,
                    content: lyricsText.value,
                    createdAt: new Date().toISOString()
                };
                
                // Obtener letras existentes o crear array vacío
                const allLyrics = MusicApp.getFromLocalStorage('musicapp_lyrics') || [];
                
                // Añadir nueva letra
                allLyrics.push(lyrics);
                
                // Guardar en localStorage
                MusicApp.saveToLocalStorage('musicapp_lyrics', allLyrics);
                
                // Mostrar mensaje de éxito
                MusicApp.showAlert('Letra guardada correctamente.', 'success');
                
                // Actualizar la biblioteca (si estamos en la misma página)
                updateLibrary();
            });
        });
    }
    
    // Función para actualizar la biblioteca de letras
    function updateLibrary() {
        const libraryTab = document.querySelector('.library-tab');
        if (!libraryTab) return;
        
        // Obtener letras guardadas
        const allLyrics = MusicApp.getFromLocalStorage('musicapp_lyrics') || [];
        
        // Elementos de la biblioteca
        const libraryEmpty = libraryTab.querySelector('.library-empty');
        let libraryContent = libraryTab.querySelector('.library-content');
        
        // Si no hay letras, mostrar mensaje vacío
        if (allLyrics.length === 0) {
            if (libraryEmpty) {
                libraryEmpty.style.display = 'block';
            }
            if (libraryContent) {
                libraryContent.remove();
            }
            return;
        }
        
        // Ocultar mensaje vacío
        if (libraryEmpty) {
            libraryEmpty.style.display = 'none';
        }
        
        // Crear contenedor de biblioteca si no existe
        if (!libraryContent) {
            libraryContent = document.createElement('div');
            libraryContent.className = 'library-content';
            libraryTab.insertBefore(libraryContent, libraryTab.querySelector('.projects-section'));
        }
        
        // Limpiar contenido anterior
        libraryContent.innerHTML = '';
        
        // Crear grid de letras
        const lyricsGrid = document.createElement('div');
        lyricsGrid.className = 'lyrics-grid';
        
        // Añadir cada letra
        allLyrics.forEach(lyrics => {
            const card = document.createElement('div');
            card.className = 'lyrics-card';
            
            // Determinar clase de estado
            let statusClass = '';
            switch (lyrics.status) {
                case 'draft':
                    statusClass = 'status-draft';
                    break;
                case 'in-progress':
                    statusClass = 'status-progress';
                    break;
                case 'completed':
                    statusClass = 'status-completed';
                    break;
            }
            
            // Obtener nombre del género
            let genreName = '';
            if (lyricsGenre && lyrics.genre) {
                const option = lyricsGenre.querySelector(`option[value="${lyrics.genre}"]`);
                genreName = option ? option.textContent : '';
            }
            
            // Obtener nombre del estado
            let statusName = '';
            if (lyricsStatus && lyrics.status) {
                const option = lyricsStatus.querySelector(`option[value="${lyrics.status}"]`);
                statusName = option ? option.textContent : '';
            }
            
            card.innerHTML = `
                <div class="lyrics-card-header">
                    <h3>${lyrics.title}</h3>
                    <span class="status-badge ${statusClass}">${statusName}</span>
                </div>
                <div class="lyrics-card-body">
                    <p><strong>Artista/Compositor:</strong> ${lyrics.artist || 'Sin artista'}</p>
                    <p><strong>Género:</strong> ${genreName || 'No especificado'}</p>
                    <p><strong>Creada:</strong> ${MusicApp.formatDate(lyrics.createdAt)}</p>
                </div>
                <div class="lyrics-card-footer">
                    <button class="btn btn-sm btn-secondary view-btn" data-id="${lyrics.id}">
                        <i class="fas fa-eye"></i> Ver
                    </button>
                    <button class="btn btn-sm btn-secondary edit-btn" data-id="${lyrics.id}">
                        <i class="fas fa-edit"></i> Editar
                    </button>
                    <button class="btn btn-sm btn-danger delete-btn" data-id="${lyrics.id}">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;
            
            lyricsGrid.appendChild(card);
        });
        
        // Añadir grid al contenedor
        libraryContent.appendChild(lyricsGrid);
        
        // Añadir event listeners a los botones
        const viewButtons = libraryContent.querySelectorAll('.view-btn');
        const editButtons = libraryContent.querySelectorAll('.edit-btn');
        const deleteButtons = libraryContent.querySelectorAll('.delete-btn');
        
        // Ver letra
        viewButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const lyrics = allLyrics.find(l => l.id === id);
                
                if (lyrics) {
                    // Aquí se podría implementar una vista detallada de la letra
                    MusicApp.showAlert(`Visualizando letra: ${lyrics.title}`, 'info');
                }
            });
        });
        
        // Editar letra
        editButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const lyrics = allLyrics.find(l => l.id === id);
                
                if (lyrics) {
                    // Cambiar a la pestaña de editor
                    document.querySelector('.tab-btn[data-tab="editor"]').click();
                    
                    // Cargar datos en el editor
                    if (lyricsTitle) lyricsTitle.value = lyrics.title;
                    if (lyricsArtist) lyricsArtist.value = lyrics.artist || '';
                    if (lyricsGenre) lyricsGenre.value = lyrics.genre || '';
                    if (lyricsStatus) lyricsStatus.value = lyrics.status || 'draft';
                    if (lyricsText) lyricsText.value = lyrics.content;
                    
                    // Actualizar vista previa
                    updatePreview();
                    
                    MusicApp.showAlert(`Editando letra: ${lyrics.title}`, 'info');
                }
            });
        });
        
        // Eliminar letra
        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                
                // Confirmar eliminación
                if (confirm('¿Estás seguro de que deseas eliminar esta letra? Esta acción no se puede deshacer.')) {
                    // Filtrar la letra a eliminar
                    const updatedLyrics = allLyrics.filter(l => l.id !== id);
                    
                    // Guardar en localStorage
                    MusicApp.saveToLocalStorage('musicapp_lyrics', updatedLyrics);
                    
                    // Actualizar la biblioteca
                    updateLibrary();
                    
                    MusicApp.showAlert('Letra eliminada correctamente.', 'success');
                }
            });
        });
    }
    
    // Inicializar la biblioteca si el usuario está autenticado
    if (isLoggedIn) {
        updateLibrary();
    }
    
    // Función para actualizar la sección de proyectos
    function updateProjects() {
        if (!isLoggedIn) return;

        const projectsSection = document.querySelector('.projects-section');
        if (!projectsSection) return;

        // Obtener proyectos guardados
        const allProjects = MusicApp.getFromLocalStorage('musicapp_projects') || [];

        const projectsEmpty = projectsSection.querySelector('.projects-empty');
        let projectsContent = projectsSection.querySelector('.projects-content');

        // Si no hay proyectos, mostrar mensaje vacío
        if (allProjects.length === 0) {
            if (projectsEmpty) {
                projectsEmpty.style.display = 'block';
            }
            if (projectsContent) {
                projectsContent.remove();
            }
            return;
        }

        // Ocultar mensaje vacío
        if (projectsEmpty) {
            projectsEmpty.style.display = 'none';
        }

        // Crear contenedor de proyectos si no existe
        if (!projectsContent) {
            projectsContent = document.createElement('div');
            projectsContent.className = 'projects-content';
            projectsSection.appendChild(projectsContent);
        }

        // Limpiar contenido anterior
        projectsContent.innerHTML = '';

        // Crear grid de proyectos
        const projectsGrid = document.createElement('div');
        projectsGrid.className = 'projects-grid';

        // Añadir cada proyecto
        allProjects.forEach(project => {
            const card = document.createElement('div');
            card.className = 'project-card';

            card.innerHTML = `
                <div class="project-card-header">
                    <h4>${project.name}</h4>
                    <span class="project-items-count">${project.items?.length || 0} elementos</span>
                </div>
                <div class="project-card-body">
                    <p>${project.description || 'Sin descripción'}</p>
                    <p><strong>Creado:</strong> ${MusicApp.formatDate(project.createdAt)}</p>
                </div>
                <div class="project-card-footer">
                    <button class="btn btn-sm btn-secondary view-project-btn" data-id="${project.id}">
                        <i class="fas fa-eye"></i> Ver
                    </button>
                    <button class="btn btn-sm btn-secondary edit-project-btn" data-id="${project.id}">
                        <i class="fas fa-edit"></i> Editar
                    </button>
                    <button class="btn btn-sm btn-danger delete-project-btn" data-id="${project.id}">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;

            projectsGrid.appendChild(card);
        });

        projectsContent.appendChild(projectsGrid);

        // Añadir event listeners a los botones de proyectos
        const viewProjectButtons = projectsContent.querySelectorAll('.view-project-btn');
        const editProjectButtons = projectsContent.querySelectorAll('.edit-project-btn');
        const deleteProjectButtons = projectsContent.querySelectorAll('.delete-project-btn');

        viewProjectButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const project = allProjects.find(p => p.id === id);
                if (project) {
                    MusicApp.showAlert(`Visualizando proyecto: ${project.name}`, 'info');
                }
            });
        });

        editProjectButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const project = allProjects.find(p => p.id === id);
                if (project) {
                    MusicApp.showAlert(`Editando proyecto: ${project.name}`, 'info');
                }
            });
        });

        deleteProjectButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                if (confirm('¿Estás seguro de que deseas eliminar este proyecto?')) {
                    const updatedProjects = allProjects.filter(p => p.id !== id);
                    MusicApp.saveToLocalStorage('musicapp_projects', updatedProjects);
                    updateProjects();
                    MusicApp.showAlert('Proyecto eliminado correctamente.', 'success');
                }
            });
        });
    }

    // Botón para crear nuevo proyecto
    const newProjectButton = document.querySelector('.projects-section .btn-sm');
    if (newProjectButton) {
        newProjectButton.addEventListener('click', function() {
            if (!isLoggedIn) {
                MusicApp.showAlert('Debes iniciar sesión para crear proyectos.', 'error');
                return;
            }

            // Crear un proyecto de ejemplo
            const projectName = prompt('Nombre del proyecto:');
            if (projectName && projectName.trim()) {
                const newProject = {
                    id: MusicApp.generateId(),
                    name: projectName.trim(),
                    description: 'Nuevo proyecto musical',
                    items: [],
                    createdAt: new Date().toISOString()
                };

                const allProjects = MusicApp.getFromLocalStorage('musicapp_projects') || [];
                allProjects.push(newProject);
                MusicApp.saveToLocalStorage('musicapp_projects', allProjects);

                updateProjects();
                MusicApp.showAlert('Proyecto creado correctamente.', 'success');
            }
        });
    }

    // Inicializar proyectos si el usuario está autenticado
    if (isLoggedIn) {
        updateProjects();
    }
});
