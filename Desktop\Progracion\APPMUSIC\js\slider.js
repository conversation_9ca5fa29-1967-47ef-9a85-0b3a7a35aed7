// JavaScript para el slider - MusicApp

document.addEventListener('DOMContentLoaded', function() {
    // Elementos del slider
    const slider = document.querySelector('.slider');
    const slides = document.querySelectorAll('.slide');
    const dots = document.querySelectorAll('.dot');
    const prevBtn = document.querySelector('.prev-btn');
    const nextBtn = document.querySelector('.next-btn');

    if (!slider || slides.length === 0) return;

    let currentSlide = 0;
    let slideInterval;
    const intervalTime = 5000; // Tiempo entre slides (5 segundos)
    
    // Función para mostrar un slide específico
    function showSlide(index) {
        // Ocultar todos los slides
        slides.forEach(slide => {
            slide.classList.remove('active');
        });
        
        // Desactivar todos los dots
        dots.forEach(dot => {
            dot.classList.remove('active');
        });
        
        // Mostrar el slide actual
        slides[index].classList.add('active');
        dots[index].classList.add('active');
        
        // Actualizar el índice actual
        currentSlide = index;
    }
    
    // Función para ir al siguiente slide
    function nextSlide() {
        let next = currentSlide + 1;
        if (next >= slides.length) {
            next = 0;
        }
        showSlide(next);
    }
    
    // Función para ir al slide anterior
    function prevSlide() {
        let prev = currentSlide - 1;
        if (prev < 0) {
            prev = slides.length - 1;
        }
        showSlide(prev);
    }
    
    // Iniciar el intervalo automático
    function startSlideInterval() {
        slideInterval = setInterval(nextSlide, intervalTime);
    }
    
    // Detener el intervalo automático
    function stopSlideInterval() {
        clearInterval(slideInterval);
    }
    
    // Event listeners para los botones
    if (prevBtn) {
        prevBtn.addEventListener('click', function() {
            prevSlide();
            stopSlideInterval();
            startSlideInterval();
        });
    }
    
    if (nextBtn) {
        nextBtn.addEventListener('click', function() {
            nextSlide();
            stopSlideInterval();
            startSlideInterval();
        });
    }
    
    // Event listeners para los dots
    dots.forEach((dot, index) => {
        dot.addEventListener('click', function() {
            showSlide(index);
            stopSlideInterval();
            startSlideInterval();
        });
    });
    
    // Event listeners para pausar el slider al pasar el mouse
    slider.addEventListener('mouseenter', stopSlideInterval);
    slider.addEventListener('mouseleave', startSlideInterval);
    
    // Event listeners para swipe en dispositivos táctiles
    let touchStartX = 0;
    let touchEndX = 0;
    
    slider.addEventListener('touchstart', function(e) {
        touchStartX = e.changedTouches[0].screenX;
    });
    
    slider.addEventListener('touchend', function(e) {
        touchEndX = e.changedTouches[0].screenX;
        handleSwipe();
    });
    
    function handleSwipe() {
        const swipeThreshold = 50; // Umbral mínimo para considerar un swipe
        
        if (touchEndX < touchStartX - swipeThreshold) {
            // Swipe izquierda (siguiente slide)
            nextSlide();
            stopSlideInterval();
            startSlideInterval();
        }
        
        if (touchEndX > touchStartX + swipeThreshold) {
            // Swipe derecha (slide anterior)
            prevSlide();
            stopSlideInterval();
            startSlideInterval();
        }
    }
    
    // Iniciar el slider
    showSlide(0);
    startSlideInterval();
});
