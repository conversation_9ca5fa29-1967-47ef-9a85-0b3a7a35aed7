// Servicio de API de acordes - Sistema simple con URLs directas
// Usa URLs directas conocidas y JSONs locales como respaldo

class ChordAPIService {
    constructor() {
        this.cache = new Map();
        this.fallbackData = null;
    }

    // Inicializar el servicio
    async init() {
        console.log('🎸 Inicializando ChordAPIService...');

        // Cargar datos de respaldo
        await this.loadFallbackData();

        console.log('✅ ChordAPIService inicializado');
        return true;
    }

    // Cargar datos de respaldo desde JSONs locales
    async loadFallbackData() {
        try {
            const isInPagesFolder = window.location.pathname.includes('/pages/');
            const basePath = isInPagesFolder ? '../' : './';

            const promises = [
                fetch(`${basePath}data/images-guitarra.json`).then(r => r.json()),
                fetch(`${basePath}data/images-piano.json`).then(r => r.json()),
                fetch(`${basePath}data/images-bajo.json`).then(r => r.json())
            ];

            const [guitarImages, pianoImages, bassImages] = await Promise.all(promises);

            this.fallbackData = {
                guitar: guitarImages,
                piano: pianoImages,
                bass: bassImages
            };

            console.log('✅ Datos de respaldo cargados correctamente');
        } catch (error) {
            console.warn('⚠️ Error cargando datos de respaldo:', error);
            this.fallbackData = { guitar: {}, piano: {}, bass: {} };
        }
    }

    // Obtener imagen de acorde desde URLs directas
    async getChordImage(instrument, root, type = 'major') {
        const cacheKey = `${instrument}-${root}-${type}`;

        // Verificar caché
        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }

        try {
            // Intentar con URLs directas primero
            const directUrlResult = await this.getFromDirectUrl(instrument, root, type);
            if (directUrlResult.success) {
                this.cache.set(cacheKey, directUrlResult);
                return directUrlResult;
            }

            // Fallback a datos locales
            const fallbackResult = this.getFromFallback(instrument, root, type);
            this.cache.set(cacheKey, fallbackResult);
            return fallbackResult;

        } catch (error) {
            console.error('Error obteniendo imagen de acorde:', error);

            // Fallback en caso de error
            const fallbackResult = this.getFromFallback(instrument, root, type);
            this.cache.set(cacheKey, fallbackResult);
            return fallbackResult;
        }
    }

    // Obtener desde URLs directas
    async getFromDirectUrl(instrument, root, type) {
        try {
            // Generar URL directa basada en patrones conocidos
            const imageUrl = this.generateDirectImageUrl(instrument, root, type);

            console.log(`🔗 Probando URL directa: ${imageUrl}`);

            // Verificar que la imagen existe
            const imageExists = await this.checkImageExists(imageUrl);

            if (imageExists) {
                return {
                    success: true,
                    data: {
                        imageUrl: imageUrl,
                        altText: `${root} ${type} en ${instrument}`,
                        name: `${root} ${type}`,
                        positions: [],
                        source: 'URL directa'
                    }
                };
            } else {
                throw new Error('Imagen no encontrada en URL directa');
            }
        } catch (error) {
            console.warn('⚠️ Error con URL directa:', error);
            return { success: false, error: error.message };
        }
    }

    // Generar URL directa de imagen
    generateDirectImageUrl(instrument, root, type) {
        // Usar URLs simples que sabemos que funcionan
        const chordName = this.formatChordName(root, type, instrument);

        // URLs de ejemplo que funcionan (puedes cambiar por URLs reales)
        switch (instrument) {
            case 'guitar':
                return `https://via.placeholder.com/250x300/1a1a1a/1db954?text=${encodeURIComponent(root + ' ' + type + ' Guitar')}`;
            case 'piano':
                return `https://via.placeholder.com/250x150/1a1a1a/1db954?text=${encodeURIComponent(root + ' ' + type + ' Piano')}`;
            case 'bass':
                return `https://via.placeholder.com/250x300/1a1a1a/1db954?text=${encodeURIComponent(root + ' ' + type + ' Bass')}`;
            default:
                return `https://via.placeholder.com/250x200/1a1a1a/1db954?text=${encodeURIComponent(root + ' ' + type)}`;
        }
    }

    // Formatear nombre de acorde para URLs
    formatChordName(root, type, instrument) {
        const typeMap = {
            'major': '',
            'minor': 'm',
            '7': '7',
            'maj7': 'maj7',
            'min7': 'm7',
            'dim': 'dim',
            'aug': 'aug',
            'sus2': 'sus2',
            'sus4': 'sus4'
        };

        const suffix = typeMap[type] || '';

        // Formatear según el instrumento
        switch (instrument) {
            case 'guitar':
                return `${root}${suffix}`.toLowerCase();
            case 'piano':
                return `${root.toLowerCase()}${suffix}`;
            case 'bass':
                return `${root}-${type}`.toLowerCase();
            default:
                return `${root}${suffix}`.toLowerCase();
        }
    }

    // Verificar si una imagen existe
    async checkImageExists(url) {
        try {
            const response = await fetch(url, { method: 'HEAD' });
            return response.ok;
        } catch {
            return false;
        }
    }

    // Obtener desde datos de respaldo (JSONs locales)
    getFromFallback(instrument, root, type) {
        try {
            const normalizedType = this.normalizeChordType(type);
            const instrumentData = this.fallbackData[instrument];
            
            if (instrumentData && instrumentData[root] && instrumentData[root][normalizedType]) {
                const chordInfo = instrumentData[root][normalizedType];
                
                console.log(`💾 Usando datos de respaldo: ${root} ${type} ${instrument}`);
                
                return {
                    success: true,
                    data: {
                        imageUrl: chordInfo.imageUrl,
                        altText: chordInfo.altText,
                        name: `${root} ${type}`,
                        positions: [],
                        source: 'JSON local (respaldo)'
                    }
                };
            }
            
            // Si no hay datos, generar SVG
            return this.generateFallbackSVG(instrument, root, type);
        } catch (error) {
            console.warn('⚠️ Error con datos de respaldo:', error);
            return this.generateFallbackSVG(instrument, root, type);
        }
    }

    // Generar SVG de respaldo
    generateFallbackSVG(instrument, root, type) {
        const svg = `
            <svg width="250" height="150" xmlns="http://www.w3.org/2000/svg">
                <rect width="250" height="150" fill="#1a1a1a" stroke="#333" stroke-width="2" rx="8"/>
                <text x="125" y="70" text-anchor="middle" font-family="Arial" font-size="20" font-weight="bold" fill="#1db954">${root} ${type}</text>
                <text x="125" y="95" text-anchor="middle" font-family="Arial" font-size="14" fill="#b3b3b3">${instrument}</text>
                <text x="125" y="115" text-anchor="middle" font-family="Arial" font-size="12" fill="#666">Diagrama generado</text>
            </svg>
        `;

        return {
            success: true,
            data: {
                imageUrl: `data:image/svg+xml;base64,${btoa(svg)}`,
                altText: `${root} ${type} en ${instrument}`,
                name: `${root} ${type}`,
                positions: [],
                source: 'SVG generado'
            }
        };
    }
}

// Crear instancia global
window.ChordAPIService = new ChordAPIService();
