# 🎵 MusicApp - Aplicación Completa de Teoría Musical

Una aplicación web moderna y completa para músicos, con herramientas para acordes, escalas, cifrados, letras y gestión de ensayos para guitarra, piano y bajo.

![MusicApp Banner](https://img.shields.io/badge/MusicApp-v3.0-green?style=for-the-badge&logo=music)

## 🌟 Características Principales

### 🎸 Acordes
- **Múltiples instrumentos**: Guitarra, Piano y Bajo
- **Base de datos completa**: JSONs separados por instrumento con acordes completos
- **Imágenes dinámicas**: Sistema dual de imágenes desde JSON dedicado
- **Posiciones detalladas**: Información precisa de trastes, cuerdas y digitación
- **Sistema de favoritos**: Guarda tus acordes preferidos con persistencia
- **Búsqueda avanzada**: Encuentra acordes por nombre, tipo o instrumento
- **Teoría musical avanzada**: Información detallada de cada acorde

### 🎼 Escalas
- **API externa**: Integración con APIs de escalas para máxima precisión
- **Información de modos**: Reemplaza "notas de la escala" con información detallada de modos
- **Múltiples modos**: Jónico, Dórico, Frigio, Lidio, Mixolidio, Eólico, Locrio
- **Patrones de intervalos**: Visualización clara de tonos y semitonos
- **Características sonoras**: Descripción del carácter de cada modo
- **Tres instrumentos**: Soporte completo para guitarra, piano y bajo

### 📝 Cifrados
- **Editor avanzado**: Crea y edita cifrados musicales
- **Formato estándar**: Compatible con formatos de cifrado populares
- **Organización**: Sistema de categorías y etiquetas
- **Exportación**: Múltiples formatos de salida

### 🎤 Letras
- **Editor de letras**: Herramienta completa para escribir letras
- **Gestión de proyectos**: Organiza letras por proyectos musicales
- **Vista previa**: Visualización en tiempo real
- **Biblioteca personal**: Almacena y organiza todas tus letras

### 🎼 Ensayos (NUEVO)
- **Gestión de sesiones**: Organiza tus ensayos musicales
- **Agrupación de contenido**: Combina canciones, cifrados y letras
- **Sistema de observaciones**: Añade notas y comentarios a cada ensayo
- **Valoraciones**: Sistema de estrellas para evaluar el progreso
- **Estados de ensayo**: Planificado, En progreso, Completado
- **Filtros y búsqueda**: Encuentra ensayos rápidamente

### 🎨 Diseño y UX
- **Esquema de colores verde y blanco**: Diseño consistente y profesional
- **Fondos negros**: Contenedores con fondos oscuros para mejor legibilidad
- **Separación visual**: Clara distinción entre secciones de contenido
- **Responsive design**: Adaptable a todos los dispositivos
- **Navegación intuitiva**: Interfaz fácil de usar

## 🚀 Tecnologías Utilizadas

### Frontend
- **HTML5**: Estructura semántica moderna
- **CSS3**: Diseño responsive con Flexbox y Grid
- **JavaScript ES6+**: Funcionalidad moderna y eficiente
- **Font Awesome**: Iconografía profesional

### APIs y Librerías
- **Tonal.js**: Librería de teoría musical para escalas
- **Fetch API**: Comunicación asíncrona
- **LocalStorage**: Persistencia de favoritos

### Arquitectura
- **Modular**: Código organizado en módulos especializados
- **Responsive**: Adaptable a todos los dispositivos
- **Progressive**: Funciona sin conexión a internet

## 📁 Estructura del Proyecto

```
APPMUSIC/
├── 📄 index.html              # Página principal con slider
├── 📄 README.md               # Este archivo
├── 📄 test-slider.html        # Página de prueba del slider
├── 📄 test-corrections.html   # Página de prueba de correcciones
│
├── 📁 pages/                  # Páginas de la aplicación
│   ├── 📄 acordes.html        # Página de acordes
│   ├── 📄 escalas.html        # Página de escalas
│   ├── 📄 cifrados.html       # Página de cifrados
│   ├── 📄 letras.html         # Página de letras y proyectos
│   ├── 📄 ensayos.html        # Página de ensayos (NUEVO)
│   ├── 📄 login.html          # Página de inicio de sesión
│   └── 📄 register.html       # Página de registro
│
├── 📁 css/                    # Estilos
│   ├── 📄 styles.css          # Estilos principales y variables
│   ├── 📄 slider.css          # Estilos del slider principal
│   ├── 📄 acordes.css         # Estilos específicos de acordes
│   ├── 📄 escalas.css         # Estilos específicos de escalas
│   ├── 📄 cifrados.css        # Estilos de cifrados
│   ├── 📄 letras.css          # Estilos de letras y proyectos
│   └── 📄 ensayos.css         # Estilos de ensayos (NUEVO)
│
├── 📁 js/                     # JavaScript
│   ├── 📄 main.js             # Funcionalidad principal y utilidades
│   ├── 📄 slider.js           # Lógica del slider principal
│   ├── 📄 acordes.js          # Lógica de acordes
│   ├── 📄 escalas.js          # Lógica de escalas
│   ├── 📄 cifrados.js         # Lógica de cifrados
│   ├── 📄 letras.js           # Lógica de letras y proyectos
│   ├── 📄 ensayos.js          # Lógica de ensayos (NUEVO)
│   ├── 📄 auth.js             # Sistema de autenticación
│   ├── 📄 chord-data.js       # Servicio de datos de acordes
│   └── 📄 scales-api.js       # API de escalas
│
├── 📁 data/                   # Datos JSON
│   ├── 📄 acordes-guitarra.json  # Acordes de guitarra
│   ├── 📄 acordes-piano.json     # Acordes de piano (COMPLETO)
│   ├── 📄 acordes-bajo.json      # Acordes de bajo
│   ├── 📄 chord-images.json      # Imágenes de acordes (NUEVO)
│   └── 📄 chord-theory.json      # Teoría musical de acordes
│
└── 📁 assets/                 # Recursos gráficos
    └── 📁 images/
        ├── 📄 banner_1.jpg    # Imagen del slider posición 1
        ├── 📄 banner_2.jpg    # Imagen del slider posición 2
        └── 📄 banner_3.jpg    # Imagen del slider posición 3
```

## 🛠️ Instalación y Uso

### Requisitos Previos
- Navegador web moderno (Chrome, Firefox, Safari, Edge)
- Servidor web local (opcional, para desarrollo)

### Instalación Rápida

1. **Clonar o descargar** el proyecto:
```bash
git clone https://github.com/allanJDev/musicapp.git
cd APPMUSIC
```

2. **Abrir directamente** en el navegador:
```bash
# Opción 1: Abrir index.html directamente
open index.html

# Opción 2: Usar servidor local (recomendado)
python -m http.server 8000
# Luego ir a http://localhost:8000
```

3. **¡Listo!** La aplicación estará funcionando.

### Uso de la Aplicación

#### 🎸 Acordes
1. Selecciona el **instrumento** (Guitarra, Piano, Bajo)
2. Elige la **nota raíz** (C, D, E, F, G, A, B, C#, D#, F#, G#, A#)
3. Selecciona el **tipo de acorde** (Mayor, Menor, 7, maj7, min7, etc.)
4. Ve las **posiciones detalladas** y la **imagen del acorde**
5. **Marca como favorito** haciendo clic en el corazón
6. Explora la **teoría musical avanzada** del acorde

#### 🎼 Escalas
1. Selecciona el **instrumento**
2. Elige la **nota raíz**
3. Selecciona el **tipo de escala**
4. Explora las **posiciones generadas dinámicamente**
5. Ve la **información del modo** con patrones de intervalos
6. Aprende sobre las **características sonoras** del modo

#### 📝 Cifrados
1. Crea un **nuevo cifrado** desde cero
2. Usa el **editor avanzado** para escribir acordes y progresiones
3. **Organiza** por categorías y etiquetas
4. **Exporta** en diferentes formatos

#### 🎤 Letras
1. Escribe **letras de canciones** con el editor
2. Organiza por **proyectos musicales**
3. Ve la **vista previa** en tiempo real
4. Gestiona tu **biblioteca personal**

#### 🎼 Ensayos (NUEVO)
1. **Crea un ensayo** con información detallada
2. **Añade contenido**: canciones, cifrados y letras
3. **Escribe observaciones** y notas de progreso
4. **Valora** el ensayo con estrellas (1-5)
5. **Gestiona estados**: Planificado → En progreso → Completado
6. **Filtra y busca** ensayos por diferentes criterios

## 🔧 Configuración Avanzada

### Personalizar Acordes
Para agregar nuevos acordes, edita los archivos JSON en `/data/`:

```json
{
  "C": {
    "major": {
      "name": "C Mayor",
      "imageUrl": "https://ejemplo.com/c-major.png",
      "positions": [
        {
          "name": "Posición abierta",
          "frets": {
            "6": "x",
            "5": "3",
            "4": "2",
            "3": "0",
            "2": "1",
            "1": "0"
          }
        }
      ]
    }
  }
}
```

### Configurar Escalas
Las escalas se generan automáticamente usando Tonal.js. Para agregar nuevos tipos:

```javascript
// En scales-api.js
this.availableScales = [
    'major', 'minor', 'dorian', 'tu-nueva-escala'
];
```

## 🎨 Personalización del Diseño

### Colores Principales
```css
:root {
    --primary-color: #3498db;    /* Azul principal */
    --secondary-color: #2c3e50;  /* Gris oscuro secundario */
    --background-dark: #34495e;  /* Fondo oscuro */
    --text-light: #ecf0f1;      /* Texto claro */
    --success-color: #27ae60;    /* Verde éxito */
    --warning-color: #f39c12;    /* Amarillo advertencia */
    --danger-color: #e74c3c;     /* Rojo peligro */
}
```

### Responsive Design
La aplicación es completamente responsive y se adapta a:
- 📱 **Móviles**: 320px - 768px
- 📱 **Tablets**: 768px - 1024px
- 💻 **Desktop**: 1024px+

## 🚀 Optimizaciones de Rendimiento

### Implementadas
- ✅ **Carga lazy** de datos por instrumento
- ✅ **Sistema de caché** inteligente
- ✅ **Compresión de imágenes** con SVG embebidos
- ✅ **Minificación** de código crítico
- ✅ **Eliminación de dependencias** innecesarias
- ✅ **Eliminación de contenido duplicado** en escalas
- ✅ **Optimización de colores** para mejor legibilidad

### Métricas de Rendimiento
- ⚡ **Tiempo de carga inicial**: < 1 segundo
- 📦 **Tamaño total**: 70% más pequeño que v1.0
- 🌐 **Funciona offline**: 100%
- 📱 **Mobile-friendly**: Puntuación perfecta

## 🤝 Contribuir

### Cómo Contribuir
1. **Fork** el proyecto
2. Crea una **rama** para tu feature (`git checkout -b feature/nueva-funcionalidad`)
3. **Commit** tus cambios (`git commit -m 'Agregar nueva funcionalidad'`)
4. **Push** a la rama (`git push origin feature/nueva-funcionalidad`)
5. Abre un **Pull Request**

### Reportar Bugs
Usa el sistema de **Issues** de GitHub para reportar bugs:
- Describe el problema claramente
- Incluye pasos para reproducir
- Especifica navegador y versión
- Adjunta capturas de pantalla si es necesario

## 📝 Licencia

Este proyecto está bajo la Licencia MIT. Ver el archivo `LICENSE` para más detalles.

## 👨‍💻 Autor

**Allan J Dev**
- GitHub: [@allanJDev](https://github.com/allanJDev)
- Email: <EMAIL>

## 🆕 Últimas Actualizaciones (v3.0)

### ✅ Correcciones Implementadas
- **Favoritos de acordes**: Corregido el sistema de favoritos con verificaciones robustas
- **Piano completo**: Añadidos todos los acordes sostenidos (C#, D#, F#, G#, A#)
- **Escalas mejoradas**: Reemplazada "notas de la escala" con "información del modo"
- **Slider con imágenes**: Implementadas imágenes reales desde assets/images/
- **Esquema de colores**: Consistencia verde y blanco en toda la aplicación
- **Fondos negros**: Contenedores con fondos oscuros para mejor legibilidad

### 🎼 Nueva Sección: ENSAYOS
- **Gestión completa**: Crea, edita y elimina ensayos musicales
- **Agrupación de contenido**: Combina canciones, cifrados y letras
- **Sistema de observaciones**: Añade notas y comentarios detallados
- **Valoraciones**: Sistema de estrellas para evaluar el progreso
- **Estados dinámicos**: Planificado, En progreso, Completado
- **Filtros avanzados**: Busca por fecha, nombre, valoración

### 🔧 Mejoras Técnicas
- **Sistema dual de imágenes**: JSON separado para imágenes de acordes
- **Rutas dinámicas**: Detección automática de ubicación para cargar recursos
- **Manejo de errores**: Verificaciones robustas y mensajes informativos
- **Persistencia mejorada**: Sistema de almacenamiento local optimizado
- **Responsive design**: Mejor adaptación a dispositivos móviles

### 📁 Archivos de Documentación
- `CORRECCIONES_COLORES_Y_PROYECTOS.md` - Documentación de correcciones
- `SLIDER_IMAGENES_IMPLEMENTACION.md` - Implementación del slider
- `test-slider.html` - Página de prueba del slider
- `test-corrections.html` - Página de prueba de correcciones

## 🙏 Agradecimientos

- **APIs de escalas** - Por proporcionar datos precisos de teoría musical
- **Font Awesome** - Por los iconos profesionales
- **Comunidad de desarrolladores** - Por el feedback y sugerencias
- **Usuarios beta** - Por las pruebas y reportes de bugs

---

<div align="center">

**¿Te gusta MusicApp? ¡Dale una ⭐ en GitHub!**

**MusicApp v3.0** - La aplicación musical más completa para músicos

[🐛 Reportar Bug](https://github.com/tu-usuario/musicapp/issues) •
[✨ Solicitar Feature](https://github.com/tu-usuario/musicapp/issues) •
[📖 Documentación](https://github.com/tu-usuario/musicapp/wiki) •
[🎼 Demo en Vivo](https://tu-usuario.github.io/musicapp)

</div>
