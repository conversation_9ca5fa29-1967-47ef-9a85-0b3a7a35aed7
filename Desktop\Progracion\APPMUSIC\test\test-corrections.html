<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test de Correcciones - MusicApp</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/acordes.css">
    <link rel="stylesheet" href="css/letras.css">
    <!-- Font Awesome para iconos -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="logo">
                <h1><a href="index.html">MusicApp</a></h1>
            </div>
            <ul class="nav-links">
                <li><a href="index.html">Inicio</a></li>
                <li><a href="pages/acordes.html" class="active">Test Correcciones</a></li>
            </ul>
            <div class="auth-buttons">
                <a href="pages/login.html" class="btn btn-login">Iniciar Sesión</a>
                <a href="pages/register.html" class="btn btn-register">Registrarse</a>
            </div>
        </nav>
    </header>

    <main>
        <section class="page-header">
            <div class="container">
                <h1>Test de Correcciones</h1>
                <p>Verificación de los cambios implementados</p>
            </div>
        </section>

        <section class="acordes-container">
            <div class="container">
                <!-- Test de contenedor de acordes con fondo negro -->
                <div class="chord-display">
                    <div class="chord-header">
                        <div class="chord-name">C Mayor</div>
                        <button class="favorite-btn" title="Añadir a favoritos">
                            <i class="far fa-heart"></i>
                        </button>
                    </div>
                    
                    <div class="chord-positions-container">
                        <h3>Posiciones del acorde (Fondo Negro)</h3>
                        <p>Este contenedor ahora tiene fondo negro con bordes verdes.</p>
                    </div>
                </div>

                <!-- Test de sección de favoritos con fondo negro -->
                <div class="favorites-section">
                    <h3>Mis Acordes Favoritos (Fondo Negro)</h3>
                    <div class="favorites-grid">
                        <p class="no-favorites">Contenedor de favoritos con fondo negro.</p>
                    </div>
                </div>

                <!-- Test de variaciones con fondo negro -->
                <div class="chord-variations">
                    <h3>Posiciones alternativas (Fondo Negro)</h3>
                    <div class="variations-text">
                        <div class="variation-text-card">
                            <h4>Variación 1</h4>
                            <p>Contenedor de variaciones con fondo negro.</p>
                        </div>
                    </div>
                </div>

                <!-- Test de detalles del acorde con fondo negro -->
                <div class="chord-details">
                    <h3>Información del acorde (Fondo Negro)</h3>
                    <div class="chord-details-content">
                        <div class="chord-theory">
                            <h4>Teoría</h4>
                            <p>Contenedor de teoría con fondo negro.</p>
                        </div>
                        <div class="chord-usage">
                            <h4>Uso común</h4>
                            <p>Contenedor de uso con fondo negro.</p>
                        </div>
                    </div>
                </div>

                <!-- Test de separación entre contenedores -->
                <div style="height: 50px; background: linear-gradient(to right, var(--color-primary), transparent); margin: var(--spacing-xxl) 0; border-radius: 4px; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">
                    SEPARACIÓN ENTRE CONTENEDORES
                </div>

                <!-- Test de sección de teoría musical avanzada con fondo negro -->
                <div class="theory-section">
                    <h3>Teoría Musical Avanzada (Fondo Negro)</h3>
                    <div class="theory-tabs">
                        <button class="theory-tab active">Teoría del Acorde</button>
                        <button class="theory-tab">Escalas Relacionadas</button>
                    </div>

                    <div class="theory-content">
                        <div class="theory-tab-content active">
                            <div class="theory-card">
                                <h4>Estructura del Acorde</h4>
                                <p>Tarjeta de teoría con fondo negro.</p>
                            </div>
                            <div class="theory-card">
                                <h4>Características Sonoras</h4>
                                <p>Otra tarjeta de teoría con fondo negro.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Test de sección de letras -->
        <section class="letras-container">
            <div class="container">
                <h2 style="text-align: center; color: var(--color-primary); margin-bottom: var(--spacing-xl);">Test de Sección de Letras</h2>
                
                <!-- Test de información de canción con fondo negro -->
                <div class="song-info">
                    <h3>Información de la canción (Fondo Negro)</h3>
                    <p>Este contenedor ahora tiene fondo negro.</p>
                </div>

                <!-- Test de editor con fondo negro -->
                <div class="editor-container">
                    <div class="lyrics-editor">
                        <h4 style="padding: var(--spacing-md); margin: 0; color: var(--color-primary);">Editor (Fondo Negro)</h4>
                    </div>
                    <div class="lyrics-preview">
                        <h4 style="margin: 0; color: var(--color-primary);">Vista Previa (Fondo Negro)</h4>
                        <div class="preview-content">
                            <p>Contenido de vista previa con fondo negro.</p>
                        </div>
                    </div>
                </div>

                <!-- Test de biblioteca vacía con fondo negro -->
                <div class="library-empty">
                    <i class="fas fa-microphone"></i>
                    <h3>Biblioteca Vacía (Fondo Negro)</h3>
                    <p>Contenedor de biblioteca vacía con fondo negro.</p>
                </div>

                <!-- Test de sección de proyectos con fondo negro -->
                <div class="projects-section">
                    <div class="section-header">
                        <h3>Mis proyectos (Fondo Negro)</h3>
                        <button class="btn btn-sm btn-secondary"><i class="fas fa-plus"></i> Nuevo proyecto</button>
                    </div>
                    
                    <div class="projects-grid">
                        <div class="project-card">
                            <div class="project-card-header">
                                <h4>Proyecto de Prueba</h4>
                                <span class="project-items-count">3 elementos</span>
                            </div>
                            <div class="project-card-body">
                                <p>Descripción del proyecto de prueba</p>
                                <p><strong>Creado:</strong> 15 de enero de 2024</p>
                            </div>
                            <div class="project-card-footer">
                                <button class="btn btn-sm btn-secondary"><i class="fas fa-eye"></i> Ver</button>
                                <button class="btn btn-sm btn-secondary"><i class="fas fa-edit"></i> Editar</button>
                                <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Test de esquema de colores -->
        <section style="padding: var(--spacing-xl) 0; background-color: var(--color-background-light);">
            <div class="container">
                <h2 style="text-align: center; color: var(--color-primary); margin-bottom: var(--spacing-xl);">Test de Esquema de Colores</h2>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-md);">
                    <div style="background: var(--color-primary); padding: var(--spacing-lg); border-radius: var(--border-radius-md); text-align: center; color: white;">
                        <strong>Verde Primario</strong><br>
                        Botones y acentos
                    </div>
                    <div style="background: var(--color-text); padding: var(--spacing-lg); border-radius: var(--border-radius-md); text-align: center; color: black;">
                        <strong>Blanco</strong><br>
                        Texto principal
                    </div>
                    <div style="background: var(--color-background); padding: var(--spacing-lg); border-radius: var(--border-radius-md); text-align: center; color: white; border: 1px solid var(--color-border);">
                        <strong>Negro Fondo</strong><br>
                        Contenedores principales
                    </div>
                    <div style="background: var(--color-background-light); padding: var(--spacing-lg); border-radius: var(--border-radius-md); text-align: center; color: white; border: 1px solid var(--color-border);">
                        <strong>Negro Claro</strong><br>
                        Contenedores secundarios
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="footer-content">
            <div class="footer-logo">
                <h2>MusicApp</h2>
                <p>Test de correcciones implementadas</p>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2023 MusicApp. Todos los derechos reservados.</p>
        </div>
    </footer>

    <script src="js/main.js"></script>
</body>
</html>
