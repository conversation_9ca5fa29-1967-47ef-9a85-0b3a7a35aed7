<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Slider - MusicApp</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/slider.css">
    <!-- Font Awesome para iconos -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* Estilos adicionales para la prueba */
        .test-info {
            background-color: var(--color-background);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-lg);
            margin: var(--spacing-xl) auto;
            max-width: 800px;
            border: 1px solid var(--color-border);
        }
        
        .test-info h2 {
            color: var(--color-primary);
            margin-bottom: var(--spacing-md);
        }
        
        .test-info ul {
            color: var(--color-text-secondary);
            line-height: 1.6;
        }
        
        .test-info li {
            margin-bottom: var(--spacing-sm);
        }
        
        .image-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-md);
            margin-top: var(--spacing-lg);
        }
        
        .image-card {
            background-color: var(--color-background-light);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md);
            text-align: center;
            border: 1px solid var(--color-border);
        }
        
        .image-card h4 {
            color: var(--color-primary);
            margin-bottom: var(--spacing-sm);
        }
        
        .image-preview {
            width: 100%;
            height: 100px;
            background-size: cover;
            background-position: center;
            border-radius: var(--border-radius-sm);
            margin-bottom: var(--spacing-sm);
            border: 1px solid var(--color-border);
        }
        
        .status-indicator {
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--border-radius-sm);
            font-size: var(--font-size-xs);
            font-weight: var(--font-weight-medium);
        }
        
        .status-loaded {
            background-color: var(--color-primary);
            color: var(--color-text);
        }
        
        .status-error {
            background-color: #dc3545;
            color: white;
        }
    </style>
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="logo">
                <h1><a href="index.html">MusicApp</a></h1>
            </div>
            <ul class="nav-links">
                <li><a href="index.html">Inicio</a></li>
                <li><a href="#" class="active">Test Slider</a></li>
            </ul>
        </nav>
    </header>

    <main>
        <div class="container">
            <div class="test-info">
                <h2>Test del Slider con Imágenes de Assets</h2>
                <p>Esta página verifica que las imágenes del slider se carguen correctamente desde la carpeta assets/images/</p>
                
                <h3>Imágenes utilizadas:</h3>
                <div class="image-status">
                    <div class="image-card">
                        <h4>Banner 1</h4>
                        <div class="image-preview" style="background-image: url('assets/images/banner_1.jpg');"></div>
                        <span class="status-indicator status-loaded">Slide 1</span>
                    </div>
                    <div class="image-card">
                        <h4>Banner 2</h4>
                        <div class="image-preview" style="background-image: url('assets/images/banner_2.jpg');"></div>
                        <span class="status-indicator status-loaded">Slide 2</span>
                    </div>
                    <div class="image-card">
                        <h4>Banner 3</h4>
                        <div class="image-preview" style="background-image: url('assets/images/banner_3.jpg');"></div>
                        <span class="status-indicator status-loaded">Slide 3</span>
                    </div>
                </div>
                
                <h3>Funcionalidades del slider:</h3>
                <ul>
                    <li>✅ Cambio automático cada 5 segundos</li>
                    <li>✅ Navegación con botones anterior/siguiente</li>
                    <li>✅ Navegación con puntos indicadores</li>
                    <li>✅ Pausa al pasar el mouse por encima</li>
                    <li>✅ Soporte para gestos táctiles (swipe)</li>
                    <li>✅ Imágenes de fondo desde assets/images/</li>
                    <li>✅ Responsive design</li>
                </ul>
            </div>
        </div>

        <!-- Slider principal -->
        <section class="slider-container">
            <div class="slider">
                <div class="slide active">
                    <div class="slide-content">
                        <h2>Slide 1 - Banner 1</h2>
                        <p>Imagen de fondo: assets/images/banner_1.jpg</p>
                        <a href="#" class="btn btn-cta">Botón de prueba</a>
                    </div>
                    <div class="slide-image" style="background-image: url('assets/images/banner_1.jpg');"></div>
                </div>
                <div class="slide">
                    <div class="slide-content">
                        <h2>Slide 2 - Banner 2</h2>
                        <p>Imagen de fondo: assets/images/banner_2.jpg</p>
                        <a href="#" class="btn btn-cta">Botón de prueba</a>
                    </div>
                    <div class="slide-image" style="background-image: url('assets/images/banner_2.jpg');"></div>
                </div>
                <div class="slide">
                    <div class="slide-content">
                        <h2>Slide 3 - Banner 3</h2>
                        <p>Imagen de fondo: assets/images/banner_3.jpg</p>
                        <a href="#" class="btn btn-cta">Botón de prueba</a>
                    </div>
                    <div class="slide-image" style="background-image: url('assets/images/banner_3.jpg');"></div>
                </div>
            </div>
            <div class="slider-controls">
                <button class="prev-btn"><i class="fas fa-chevron-left"></i></button>
                <div class="slider-dots">
                    <span class="dot active" data-slide="0"></span>
                    <span class="dot" data-slide="1"></span>
                    <span class="dot" data-slide="2"></span>
                </div>
                <button class="next-btn"><i class="fas fa-chevron-right"></i></button>
            </div>
        </section>

        <div class="container">
            <div class="test-info">
                <h3>Instrucciones de prueba:</h3>
                <ul>
                    <li><strong>Escritorio:</strong> Las imágenes aparecen en el lado derecho de cada slide</li>
                    <li><strong>Móvil:</strong> Las imágenes aparecen como fondo completo del slide</li>
                    <li><strong>Navegación:</strong> Usa los botones, puntos o gestos táctiles para cambiar slides</li>
                    <li><strong>Auto-play:</strong> El slider cambia automáticamente cada 5 segundos</li>
                    <li><strong>Pausa:</strong> Pasa el mouse sobre el slider para pausar el auto-play</li>
                </ul>
                
                <h3>Verificación:</h3>
                <ul>
                    <li>Si ves las imágenes correctamente, la implementación es exitosa</li>
                    <li>Si no ves imágenes, verifica que los archivos existan en assets/images/</li>
                    <li>En dispositivos móviles, las imágenes deben aparecer como fondo completo</li>
                    <li>El gradiente oscuro debe hacer que el texto sea legible sobre las imágenes</li>
                </ul>
            </div>
        </div>
    </main>

    <footer>
        <div class="footer-content">
            <div class="footer-logo">
                <h2>MusicApp</h2>
                <p>Test del slider con imágenes</p>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2023 MusicApp. Test de funcionalidad.</p>
        </div>
    </footer>

    <script src="js/slider.js"></script>
    <script src="js/main.js"></script>
    
    <script>
        // Script adicional para verificar la carga de imágenes
        document.addEventListener('DOMContentLoaded', function() {
            const imageUrls = [
                'assets/images/banner_1.jpg',
                'assets/images/banner_2.jpg',
                'assets/images/banner_3.jpg'
            ];
            
            imageUrls.forEach((url, index) => {
                const img = new Image();
                img.onload = function() {
                    console.log(`✅ Imagen ${index + 1} cargada correctamente: ${url}`);
                };
                img.onerror = function() {
                    console.error(`❌ Error al cargar imagen ${index + 1}: ${url}`);
                    // Cambiar el indicador de estado a error
                    const statusIndicators = document.querySelectorAll('.status-indicator');
                    if (statusIndicators[index]) {
                        statusIndicators[index].textContent = 'Error';
                        statusIndicators[index].className = 'status-indicator status-error';
                    }
                };
                img.src = url;
            });
        });
    </script>
</body>
</html>
