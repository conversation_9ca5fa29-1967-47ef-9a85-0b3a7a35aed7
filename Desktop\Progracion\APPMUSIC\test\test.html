<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - MusicApp</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .chord-image {
            max-width: 300px;
            height: auto;
            border: 1px solid #ccc;
            margin: 10px 0;
        }
        button {
            padding: 10px 15px;
            margin: 5px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .scale-notes {
            display: flex;
            gap: 10px;
            margin: 10px 0;
        }
        .scale-note {
            padding: 5px 10px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
        }
        .scale-note.root {
            background: #007bff;
            color: white;
        }
    </style>
</head>
<body>
    <h1>Test de Funcionalidades - MusicApp</h1>

    <div class="test-section">
        <h2>Test de Diagramas de Acordes</h2>
        <div>
            <button onclick="testChord('guitar', 'C', 'major')">C Mayor Guitarra</button>
            <button onclick="testChord('piano', 'C', 'major')">C Mayor Piano</button>
            <button onclick="testChord('guitar', 'Am', 'minor')">A Menor Guitarra</button>
            <button onclick="testChord('piano', 'G', '7')">G7 Piano</button>
        </div>
        <div id="chord-result">
            <p>Haz clic en un botón para probar los acordes</p>
        </div>
    </div>

    <div class="test-section">
        <h2>Test de Escalas y Modos</h2>
        <div>
            <button onclick="testScale('C', 'major')">C Mayor</button>
            <button onclick="testScale('D', 'dorian')">D Dórico</button>
            <button onclick="testScale('E', 'phrygian')">E Frigio</button>
            <button onclick="testScale('F', 'lydian')">F Lidio</button>
            <button onclick="testScale('G', 'mixolydian')">G Mixolidio</button>
            <button onclick="testScale('A', 'aeolian')">A Eólico</button>
            <button onclick="testScale('B', 'locrian')">B Locrio</button>
        </div>
        <div id="scale-result">
            <p>Haz clic en un botón para probar las escalas</p>
        </div>
    </div>

    <div class="test-section">
        <h2>Test de Teoría Musical</h2>
        <div id="theory-result">
            <p>La información teórica se mostrará aquí</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/chord-diagrams.js"></script>
    <script src="js/api-service.js"></script>
    <script src="js/chord-data.js"></script>

    <script>
        // Test de acordes
        function testChord(instrument, root, type) {
            console.log(`Testing chord: ${root} ${type} on ${instrument}`);
            
            const result = ChordDataService.getChordImage(instrument, root, type);
            const resultDiv = document.getElementById('chord-result');
            
            if (result.success) {
                resultDiv.innerHTML = `
                    <h3>${root} ${type} - ${instrument}</h3>
                    <img src="${result.data.imageUrl}" alt="${result.data.altText}" class="chord-image">
                    <p><strong>Fuente:</strong> ${result.data.source}</p>
                `;
            } else {
                resultDiv.innerHTML = `
                    <h3>Error</h3>
                    <p style="color: red;">${result.error}</p>
                `;
            }
        }

        // Test de escalas
        function testScale(root, type) {
            console.log(`Testing scale: ${root} ${type}`);
            
            // Datos de escalas (copiados del archivo escalas.js)
            const scaleData = {
                scales: {
                    major: { intervals: [0, 2, 4, 5, 7, 9, 11], name: 'Mayor (Jónico)' },
                    dorian: { intervals: [0, 2, 3, 5, 7, 9, 10], name: 'Dórico' },
                    phrygian: { intervals: [0, 1, 3, 5, 7, 8, 10], name: 'Frigio' },
                    lydian: { intervals: [0, 2, 4, 6, 7, 9, 11], name: 'Lidio' },
                    mixolydian: { intervals: [0, 2, 4, 5, 7, 9, 10], name: 'Mixolidio' },
                    aeolian: { intervals: [0, 2, 3, 5, 7, 8, 10], name: 'Eólico' },
                    locrian: { intervals: [0, 1, 3, 5, 6, 8, 10], name: 'Locrio' }
                },
                notes: ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B']
            };

            const scale = scaleData.scales[type];
            if (!scale) {
                document.getElementById('scale-result').innerHTML = `<p style="color: red;">Escala no encontrada: ${type}</p>`;
                return;
            }

            const rootIndex = scaleData.notes.indexOf(root);
            if (rootIndex === -1) {
                document.getElementById('scale-result').innerHTML = `<p style="color: red;">Nota raíz no válida: ${root}</p>`;
                return;
            }

            const notes = scale.intervals.map(interval => {
                const noteIndex = (rootIndex + interval) % 12;
                return scaleData.notes[noteIndex];
            });

            const resultDiv = document.getElementById('scale-result');
            resultDiv.innerHTML = `
                <h3>${root} ${scale.name}</h3>
                <div class="scale-notes">
                    ${notes.map((note, index) => 
                        `<span class="scale-note ${index === 0 ? 'root' : ''}">${note}</span>`
                    ).join('')}
                </div>
                <p><strong>Intervalos:</strong> ${scale.intervals.join(' - ')}</p>
            `;
        }

        // Test inicial
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded');
            
            // Verificar que los servicios estén disponibles
            const services = [
                'ChordDiagramGenerator',
                'ChordDataService',
                'MusicAppApi'
            ];
            
            const theoryDiv = document.getElementById('theory-result');
            let status = '<h3>Estado de los Servicios</h3><ul>';
            
            services.forEach(service => {
                const available = window[service] !== undefined;
                status += `<li><strong>${service}:</strong> ${available ? '✅ Disponible' : '❌ No disponible'}</li>`;
            });
            
            status += '</ul>';
            theoryDiv.innerHTML = status;
            
            // Test automático de un acorde
            setTimeout(() => {
                testChord('guitar', 'C', 'major');
            }, 1000);
        });
    </script>
</body>
</html>
