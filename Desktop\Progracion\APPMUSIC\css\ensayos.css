/* Estilos para la página de ensayos - MusicApp */

.ensayos-container {
    padding: var(--spacing-xl) 0;
}

.login-prompt {
    background-color: var(--color-background);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    text-align: center;
    border: 1px solid var(--color-border);
}

.login-message {
    max-width: 600px;
    margin: 0 auto;
}

.login-message i {
    font-size: 3rem;
    color: var(--color-primary);
    margin-bottom: var(--spacing-md);
}

.login-message h3 {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-md);
}

.login-message p {
    color: var(--color-text-secondary);
    margin-bottom: var(--spacing-lg);
}

.login-buttons {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
}

.ensayos-tabs {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
    border-bottom: 1px solid var(--color-border);
    padding-bottom: var(--spacing-md);
}

.tab-btn {
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: transparent;
    color: var(--color-text-secondary);
    font-weight: var(--font-weight-medium);
    border-bottom: 2px solid transparent;
    transition: all var(--transition-fast);
}

.tab-btn:hover {
    color: var(--color-text);
}

.tab-btn.active {
    color: var(--color-primary);
    border-bottom-color: var(--color-primary);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Filtros de ensayos */
.ensayos-filters {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.search-bar {
    display: flex;
    flex: 1;
    max-width: 400px;
}

.search-bar input {
    flex: 1;
    padding: var(--spacing-md);
    background-color: var(--color-background);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);
    color: var(--color-text);
}

.search-bar button {
    padding: 0 var(--spacing-md);
    background-color: var(--color-primary);
    border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
    color: var(--color-text);
}

.filter-options {
    display: flex;
    gap: var(--spacing-md);
}

.filter-select,
.sort-select {
    padding: var(--spacing-md);
    background-color: var(--color-background);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius-md);
    color: var(--color-text);
}

/* Estado vacío */
.ensayos-empty {
    text-align: center;
    padding: var(--spacing-xxl) var(--spacing-lg);
    background-color: var(--color-background);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-xl);
    border: 1px solid var(--color-border);
}

.ensayos-empty i {
    font-size: 3rem;
    color: var(--color-text-secondary);
    margin-bottom: var(--spacing-md);
}

.ensayos-empty h3 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-sm);
}

.ensayos-empty p {
    color: var(--color-text-secondary);
    margin-bottom: var(--spacing-lg);
}

.empty-actions {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
}

/* Grid de ensayos */
.ensayos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--spacing-lg);
}

.ensayo-card {
    background-color: var(--color-background);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--color-border);
    transition: all var(--transition-fast);
    cursor: pointer;
}

.ensayo-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--color-primary);
}

.ensayo-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);
}

.ensayo-title {
    color: var(--color-primary);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-bold);
    margin: 0;
}

.ensayo-rating {
    display: flex;
    gap: var(--spacing-xs);
}

.star {
    color: var(--color-text-secondary);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: color var(--transition-fast);
}

.star.filled {
    color: #ffc107;
}

.ensayo-meta {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
}

.ensayo-description {
    color: var(--color-text-secondary);
    margin-bottom: var(--spacing-md);
    line-height: 1.5;
}

.ensayo-content-summary {
    margin-bottom: var(--spacing-md);
}

.content-count {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    margin-right: var(--spacing-md);
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
}

.content-count i {
    color: var(--color-primary);
}

.ensayo-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ensayo-status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
}

.status-planned {
    background-color: #6c757d;
    color: var(--color-text);
}

.status-in-progress {
    background-color: #ffc107;
    color: #000;
}

.status-completed {
    background-color: var(--color-primary);
    color: var(--color-text);
}

.ensayo-buttons {
    display: flex;
    gap: var(--spacing-sm);
}

/* Formulario de crear ensayo */
.create-ensayo-form {
    background-color: var(--color-background);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    border: 1px solid var(--color-border);
}

.form-section {
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--color-border);
}

.form-section:last-of-type {
    border-bottom: none;
    margin-bottom: var(--spacing-lg);
}

.form-section h3 {
    color: var(--color-primary);
    margin-bottom: var(--spacing-lg);
    font-size: var(--font-size-lg);
}

.form-group {
    margin-bottom: var(--spacing-md);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: var(--font-weight-medium);
    color: var(--color-text);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-md);
    background-color: var(--color-background-light);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius-md);
    color: var(--color-text);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--color-primary);
}

.form-group textarea {
    min-height: 100px;
    resize: vertical;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
}

/* Selector de contenido */
.content-selector {
    background-color: var(--color-background-light);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    border: 1px solid var(--color-border);
}

.content-tabs {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

.content-tab {
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: var(--color-background);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius-md);
    color: var(--color-text);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.content-tab:hover {
    background-color: var(--color-background-lighter);
}

.content-tab.active {
    background-color: var(--color-primary);
    border-color: var(--color-primary);
    color: var(--color-text);
}

.content-lists {
    min-height: 200px;
}

.content-list {
    display: none;
}

.content-list.active {
    display: block;
}

.no-content {
    text-align: center;
    color: var(--color-text-secondary);
    padding: var(--spacing-xl);
}

.selected-content {
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--color-border);
}

.selected-content h4 {
    color: var(--color-primary);
    margin-bottom: var(--spacing-md);
}

.selected-items {
    min-height: 100px;
}

.no-selected {
    text-align: center;
    color: var(--color-text-secondary);
    padding: var(--spacing-lg);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-md);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--color-border);
}

/* Media Queries */
@media screen and (max-width: 768px) {
    .ensayos-filters {
        flex-direction: column;
    }

    .search-bar {
        max-width: 100%;
    }

    .filter-options {
        width: 100%;
    }

    .filter-select,
    .sort-select {
        flex: 1;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .ensayos-grid {
        grid-template-columns: 1fr;
    }

    .content-tabs {
        flex-wrap: wrap;
    }

    .form-actions {
        flex-direction: column;
    }
}

@media screen and (max-width: 480px) {
    .login-buttons {
        flex-direction: column;
    }

    .ensayo-actions {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: stretch;
    }

    .ensayo-buttons {
        justify-content: center;
    }
}
