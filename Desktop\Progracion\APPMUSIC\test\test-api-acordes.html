<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API de Acordes - MusicApp</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #121212;
            color: #f0f0f0;
            margin: 0;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #1db954;
            margin-bottom: 10px;
        }

        .controls {
            background: #1a1a1a;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 30px;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .control-group label {
            color: #1db954;
            font-weight: bold;
            font-size: 14px;
        }

        select, button {
            padding: 10px;
            border: 2px solid #333;
            border-radius: 8px;
            background: #2a2a2a;
            color: #f0f0f0;
            font-size: 14px;
        }

        button {
            background: #1db954;
            color: #000;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        button:hover {
            background: #1ed760;
            transform: translateY(-2px);
        }

        .results {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .result-card {
            background: #1a1a1a;
            border-radius: 12px;
            padding: 20px;
            border: 2px solid #333;
        }

        .result-card h3 {
            color: #1db954;
            margin-top: 0;
            margin-bottom: 15px;
        }

        .chord-image {
            width: 100%;
            max-width: 250px;
            height: auto;
            border-radius: 8px;
            margin: 10px 0;
            border: 1px solid #333;
        }

        .info-item {
            margin: 8px 0;
            padding: 8px;
            background: #2a2a2a;
            border-radius: 6px;
            border-left: 3px solid #1db954;
        }

        .info-label {
            color: #1db954;
            font-weight: bold;
            margin-right: 8px;
        }

        .source-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-top: 10px;
        }

        .source-api {
            background: #1db954;
            color: #000;
        }

        .source-local {
            background: #ff6b35;
            color: #fff;
        }

        .source-generated {
            background: #666;
            color: #fff;
        }

        .loading {
            text-align: center;
            color: #1db954;
            font-size: 18px;
            margin: 20px 0;
        }

        .error {
            background: #ff4444;
            color: #fff;
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
        }

        .log-container {
            background: #0a0a0a;
            border: 1px solid #333;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }

        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }

        .log-success { color: #1db954; }
        .log-warning { color: #ffaa00; }
        .log-error { color: #ff4444; }
        .log-info { color: #66b3ff; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎸 Test API de Acordes - Sistema Híbrido</h1>
            <p>Prueba el nuevo sistema que usa APIs gratuitas como fuente principal y JSONs como respaldo</p>
        </div>

        <div class="controls">
            <div class="control-group">
                <label for="instrument">Instrumento:</label>
                <select id="instrument">
                    <option value="guitar">Guitarra</option>
                    <option value="piano">Piano</option>
                    <option value="bass">Bajo</option>
                </select>
            </div>

            <div class="control-group">
                <label for="root">Nota Raíz:</label>
                <select id="root">
                    <option value="C">C</option>
                    <option value="C#">C#</option>
                    <option value="D">D</option>
                    <option value="D#">D#</option>
                    <option value="E">E</option>
                    <option value="F">F</option>
                    <option value="F#">F#</option>
                    <option value="G">G</option>
                    <option value="G#">G#</option>
                    <option value="A">A</option>
                    <option value="A#">A#</option>
                    <option value="B">B</option>
                </select>
            </div>

            <div class="control-group">
                <label for="type">Tipo:</label>
                <select id="type">
                    <option value="major">Mayor</option>
                    <option value="minor">Menor</option>
                    <option value="7">7</option>
                    <option value="maj7">maj7</option>
                    <option value="min7">min7</option>
                    <option value="dim">Disminuido</option>
                    <option value="aug">Aumentado</option>
                    <option value="sus2">sus2</option>
                    <option value="sus4">sus4</option>
                </select>
            </div>

            <button id="test-btn">🔍 Probar Acorde</button>
            <button id="test-all-btn">🎯 Probar Todos los Instrumentos</button>
            <button id="clear-log-btn">🗑️ Limpiar Log</button>
        </div>

        <div id="loading" class="loading" style="display: none;">
            Cargando acordes...
        </div>

        <div id="results" class="results"></div>

        <div class="log-container">
            <h3 style="color: #1db954; margin-top: 0;">📋 Log del Sistema</h3>
            <div id="log-content"></div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/main.js"></script>
    <script src="js/chord-api-service.js"></script>
    <script src="js/chord-data.js"></script>

    <script>
        // Variables globales
        let logContainer;
        let resultsContainer;
        let loadingElement;

        // Inicializar cuando el DOM esté listo
        document.addEventListener('DOMContentLoaded', async function() {
            logContainer = document.getElementById('log-content');
            resultsContainer = document.getElementById('results');
            loadingElement = document.getElementById('loading');

            // Interceptar console.log para mostrar en el log
            const originalLog = console.log;
            const originalWarn = console.warn;
            const originalError = console.error;

            console.log = function(...args) {
                originalLog.apply(console, args);
                addLogEntry(args.join(' '), 'info');
            };

            console.warn = function(...args) {
                originalWarn.apply(console, args);
                addLogEntry(args.join(' '), 'warning');
            };

            console.error = function(...args) {
                originalError.apply(console, args);
                addLogEntry(args.join(' '), 'error');
            };

            // Inicializar servicios
            addLogEntry('🚀 Iniciando sistema de pruebas...', 'info');
            
            try {
                // Inicializar API Service
                if (window.ChordAPIService) {
                    await window.ChordAPIService.init();
                    addLogEntry('✅ ChordAPIService inicializado', 'success');
                } else {
                    addLogEntry('⚠️ ChordAPIService no disponible', 'warning');
                }

                // Inicializar Data Service
                if (window.ChordDataService) {
                    await window.ChordDataService.init();
                    addLogEntry('✅ ChordDataService inicializado', 'success');
                } else {
                    addLogEntry('❌ ChordDataService no disponible', 'error');
                }

                addLogEntry('🎉 Sistema listo para pruebas', 'success');
            } catch (error) {
                addLogEntry(`❌ Error inicializando: ${error.message}`, 'error');
            }

            // Event listeners
            document.getElementById('test-btn').addEventListener('click', testSingleChord);
            document.getElementById('test-all-btn').addEventListener('click', testAllInstruments);
            document.getElementById('clear-log-btn').addEventListener('click', clearLog);
        });

        // Añadir entrada al log
        function addLogEntry(message, type = 'info') {
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(entry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // Limpiar log
        function clearLog() {
            logContainer.innerHTML = '';
            addLogEntry('📋 Log limpiado', 'info');
        }

        // Probar un solo acorde
        async function testSingleChord() {
            const instrument = document.getElementById('instrument').value;
            const root = document.getElementById('root').value;
            const type = document.getElementById('type').value;

            addLogEntry(`🎸 Probando: ${root} ${type} en ${instrument}`, 'info');
            
            showLoading(true);
            resultsContainer.innerHTML = '';

            try {
                const result = await window.ChordDataService.getChordImage(instrument, root, type);
                displayResult(instrument, root, type, result);
            } catch (error) {
                addLogEntry(`❌ Error: ${error.message}`, 'error');
            } finally {
                showLoading(false);
            }
        }

        // Probar todos los instrumentos
        async function testAllInstruments() {
            const root = document.getElementById('root').value;
            const type = document.getElementById('type').value;
            const instruments = ['guitar', 'piano', 'bass'];

            addLogEntry(`🎯 Probando ${root} ${type} en todos los instrumentos`, 'info');
            
            showLoading(true);
            resultsContainer.innerHTML = '';

            for (const instrument of instruments) {
                try {
                    const result = await window.ChordDataService.getChordImage(instrument, root, type);
                    displayResult(instrument, root, type, result);
                } catch (error) {
                    addLogEntry(`❌ Error con ${instrument}: ${error.message}`, 'error');
                }
            }

            showLoading(false);
        }

        // Mostrar resultado
        function displayResult(instrument, root, type, result) {
            const card = document.createElement('div');
            card.className = 'result-card';

            const instrumentName = {
                'guitar': 'Guitarra',
                'piano': 'Piano', 
                'bass': 'Bajo'
            }[instrument];

            let content = `<h3>${root} ${type} - ${instrumentName}</h3>`;

            if (result.success && result.data) {
                const data = result.data;
                
                content += `
                    <img src="${data.imageUrl}" alt="${data.altText}" class="chord-image" 
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <div style="display:none; color:#ff4444;">❌ Error cargando imagen</div>
                    
                    <div class="info-item">
                        <span class="info-label">Nombre:</span>${data.name}
                    </div>
                    
                    <div class="info-item">
                        <span class="info-label">Alt Text:</span>${data.altText}
                    </div>
                    
                    <div class="info-item">
                        <span class="info-label">Posiciones:</span>${data.positions ? data.positions.length : 0}
                    </div>
                    
                    <div class="source-badge ${getSourceClass(data.source)}">${data.source}</div>
                `;
            } else {
                content += `<div class="error">❌ Error: ${result.error || 'Desconocido'}</div>`;
            }

            card.innerHTML = content;
            resultsContainer.appendChild(card);
        }

        // Obtener clase CSS para la fuente
        function getSourceClass(source) {
            if (source.includes('API')) return 'source-api';
            if (source.includes('local') || source.includes('respaldo')) return 'source-local';
            return 'source-generated';
        }

        // Mostrar/ocultar loading
        function showLoading(show) {
            loadingElement.style.display = show ? 'block' : 'none';
        }
    </script>
</body>
</html>
