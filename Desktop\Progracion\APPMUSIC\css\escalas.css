/* Estilos para la página de escalas - MusicApp */

.escalas-container {
    padding: var(--spacing-xl) 0;
}

.instrument-selector {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.btn-instrument {
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--border-radius-md);
    background-color: var(--color-background-light);
    color: var(--color-text);
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-fast);
    border: 1px solid var(--color-border);
}

.btn-instrument i {
    margin-right: var(--spacing-sm);
}

.btn-instrument:hover {
    background-color: var(--color-background-lighter);
}

.btn-instrument.active {
    background-color: var(--color-primary);
    border-color: var(--color-primary);
}

.scale-controls {
    display: flex;
    justify-content: center;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.scale-selector {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.scale-selector label {
    font-weight: var(--font-weight-medium);
    color: var(--color-text);
}

.scale-select {
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    background-color: var(--color-background);
    color: var(--color-text);
    border: 1px solid var(--color-border);
    min-width: 150px;
}

.scale-select:focus {
    outline: none;
    border-color: var(--color-primary);
}

.scale-display {
    background-color: var(--color-background-light);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    box-shadow: var(--shadow-sm);
}

.scale-name {
    font-size: var(--font-size-xxl);
    font-weight: var(--font-weight-bold);
    text-align: center;
    margin-bottom: var(--spacing-lg);
    color: var(--color-primary);
}

.scale-diagram {
    display: none;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-lg);
}

.scale-diagram.active {
    display: flex;
}

/* Estilos para diagrama de guitarra */
.guitar-neck {
    position: relative;
    width: 100%;
    max-width: 800px;
    height: 250px;
    background-color: #2C2C2C;
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-md);
    margin: 0 auto;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    border: 1px solid #444;
}

.guitar-strings {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
}

.guitar-string {
    height: 2px;
    background-color: #B3B3B3;
    width: 100%;
    position: relative;
    box-shadow: 0 1px 2px rgba(255, 255, 255, 0.1);
}

.guitar-string:nth-child(1),
.guitar-string:nth-child(2) {
    background-color: #D9D9D9;
    height: 1px;
}

.guitar-string:nth-child(3),
.guitar-string:nth-child(4) {
    height: 2px;
}

.guitar-string:nth-child(5),
.guitar-string:nth-child(6) {
    height: 3px;
}

.guitar-frets {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: var(--spacing-md);
    pointer-events: none;
}

.guitar-fret {
    width: 3px;
    background-color: #505050;
    height: 100%;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

.scale-notes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.scale-note {
    position: absolute;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background-color: var(--color-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-text);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-bold);
    transform: translate(-50%, -50%);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    z-index: 5;
}

.scale-note.root {
    background-color: #FF5252;
    box-shadow: 0 0 8px #FF5252;
}

/* Estilos para diagrama de piano */
.piano-keyboard {
    width: 100%;
    max-width: 800px;
    height: 180px;
    position: relative;
    margin: 0 auto;
    perspective: 1000px;
}

.piano-keys {
    display: flex;
    height: 100%;
    position: relative;
    width: 100%;
    justify-content: center;
    transform-style: preserve-3d;
    transform: rotateX(5deg);
}

.piano-key {
    position: relative;
    border: 1px solid #000;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.piano-key.white {
    background-color: #f8f9fa;
    width: 45px;
    height: 100%;
    z-index: 1;
    position: relative;
    border-radius: 0 0 4px 4px;
    margin: 0 1px;
    background: linear-gradient(to bottom, #f8f9fa 0%, #e9ecef 100%);
}

.piano-key.black {
    background-color: #000;
    width: 30px;
    height: 65%;
    position: absolute;
    z-index: 2;
    border-radius: 0 0 3px 3px;
    background: linear-gradient(to bottom, #333 0%, #000 100%);
    box-shadow: 0 3px 5px rgba(0, 0, 0, 0.5);
}

.piano-key.active {
    background: var(--color-primary);
    box-shadow: 0 0 10px var(--color-primary);
}

.piano-key.white.active {
    background: linear-gradient(to bottom, var(--color-primary) 0%, #1aa34a 100%);
}

.piano-key.black.active {
    background: linear-gradient(to bottom, #2ad65a 0%, var(--color-primary) 100%);
}

.piano-key.root {
    background: #FF5252;
    box-shadow: 0 0 10px #FF5252;
}

.piano-key.white.root {
    background: linear-gradient(to bottom, #FF5252 0%, #e63946 100%);
}

.piano-key.black.root {
    background: linear-gradient(to bottom, #ff7b7b 0%, #FF5252 100%);
}

/* Estilos para diagrama de bajo */
.bass-neck {
    position: relative;
    width: 100%;
    max-width: 800px;
    height: 200px;
    background-color: #2C2C2C;
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-md);
    margin: 0 auto;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    border: 1px solid #444;
}

.bass-strings {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
}

.bass-string {
    height: 3px;
    background-color: #B3B3B3;
    width: 100%;
    position: relative;
    box-shadow: 0 1px 2px rgba(255, 255, 255, 0.1);
}

.bass-string:nth-child(1) {
    height: 2px;
}

.bass-string:nth-child(2) {
    height: 3px;
}

.bass-string:nth-child(3) {
    height: 4px;
}

.bass-string:nth-child(4) {
    height: 5px;
}

.bass-frets {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: var(--spacing-md);
    pointer-events: none;
}

.bass-fret {
    width: 3px;
    background-color: #505050;
    height: 100%;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

.scale-info {
    display: flex;
    justify-content: center;
    margin-top: var(--spacing-xl);
    gap: var(--spacing-xl);
    flex-wrap: wrap;
}

.scale-notes-list,
.scale-formula {
    flex: 1;
    min-width: 250px;
    background-color: var(--color-background);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
}

.scale-notes-list h3,
.scale-formula h3 {
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-lg);
}

.notes-container {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.scale-note {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--color-background-light);
    color: var(--color-text);
    font-weight: var(--font-weight-medium);
}

.scale-note.root {
    background-color: var(--color-primary);
}

.scale-formula p {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-sm);
    color: var(--color-text);
}

.formula-explanation {
    font-size: var(--font-size-sm) !important;
    color: var(--color-text-secondary) !important;
}

.scale-positions {
    margin-top: var(--spacing-xl);
    text-align: center;
}

.scale-positions h3 {
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-lg);
}

.positions-grid {
    display: flex;
    justify-content: center;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.position-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: var(--color-background);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius-md);
    color: var(--color-text);
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-fast);
}

.position-btn:hover {
    background-color: var(--color-background-lighter);
}

.position-btn.active {
    background-color: var(--color-primary);
    border-color: var(--color-primary);
}

.related-scales {
    margin-top: var(--spacing-xxl);
    padding: var(--spacing-xl) 0;
    background-color: var(--color-background-light);
}

.related-scales h3 {
    text-align: center;
    margin-bottom: var(--spacing-lg);
    font-size: var(--font-size-lg);
}

.related-scales-grid {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.related-scale {
    display: inline-block;
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: var(--color-background);
    border-radius: var(--border-radius-md);
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-fast);
}

.related-scale:hover {
    background-color: var(--color-primary);
    color: var(--color-text);
}

/* Media Queries */
@media screen and (max-width: 768px) {
    .scale-controls {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-md);
    }

    .scale-selector {
        width: 100%;
        max-width: 300px;
    }

    .guitar-neck,
    .bass-neck {
        height: 200px;
    }

    .piano-keyboard {
        height: 150px;
    }

    .scale-info {
        flex-direction: column;
    }
}

@media screen and (max-width: 480px) {
    .instrument-selector {
        flex-direction: column;
        align-items: center;
    }

    .btn-instrument {
        width: 100%;
        max-width: 250px;
    }

    .positions-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Estilos para las posiciones de texto de escalas */
.scale-positions-text {
    position: relative;
}

.instrument-positions {
    display: none;
}

.instrument-positions.active {
    display: block;
}

.instrument-positions h3 {
    color: #007bff;
    margin-bottom: 15px;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.position-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
}

.position-info h4 {
    color: #495057;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.position-details p {
    margin: 8px 0;
    padding: 5px 0;
    border-bottom: 1px solid #e9ecef;
    font-size: 0.95rem;
}

.position-details p:last-child {
    border-bottom: none;
}

.position-details strong {
    color: #007bff;
    min-width: 120px;
    display: inline-block;
}

/* Estilos para patrones de digitación */
.scale-patterns {
    margin-top: 20px;
}

.scale-patterns h3 {
    color: #495057;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.patterns-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.pattern-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    border-left: 4px solid #28a745;
}

.pattern-card h4 {
    color: #495057;
    margin-bottom: 10px;
    font-size: 1rem;
}

.pattern-card p {
    margin: 5px 0;
    font-size: 0.9rem;
    color: #6c757d;
}

.pattern-card strong {
    color: #28a745;
}

/* Estilos para la información del modo */
.scale-mode-info {
    background-color: var(--color-background);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    border: 1px solid var(--color-border);
}

.scale-mode-info h3 {
    text-align: center;
    margin-bottom: var(--spacing-md);
    color: var(--color-primary);
}

.mode-details {
    display: grid;
    gap: var(--spacing-lg);
}

.mode-characteristic {
    background-color: var(--color-background-light);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    border: 1px solid var(--color-border);
}

.mode-characteristic h4 {
    color: var(--color-primary);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-md);
}

.mode-characteristic #current-mode {
    color: var(--color-text);
    font-weight: var(--font-weight-bold);
}

.mode-characteristic p {
    color: var(--color-text-secondary);
    line-height: 1.6;
}

.mode-intervals {
    background-color: var(--color-background-light);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    border: 1px solid var(--color-border);
}

.mode-intervals h4 {
    color: var(--color-primary);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-md);
}

.intervals-container {
    display: flex;
    justify-content: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    flex-wrap: wrap;
}

.interval {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: var(--color-background);
    border: 2px solid var(--color-primary);
    border-radius: var(--border-radius-circle);
    font-weight: var(--font-weight-bold);
    font-size: var(--font-size-sm);
    color: var(--color-primary);
    transition: all var(--transition-fast);
}

.interval:hover {
    background-color: var(--color-primary);
    color: var(--color-text);
    transform: scale(1.1);
}

.intervals-legend {
    text-align: center;
    color: var(--color-text-secondary);
    font-size: var(--font-size-sm);
    margin: 0;
}
