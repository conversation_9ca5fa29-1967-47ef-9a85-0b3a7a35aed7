/* Estilos para el slider - MusicApp */

.slider-container {
    position: relative;
    width: 100%;
    height: 500px;
    overflow: hidden;
    margin-bottom: var(--spacing-xxl);
}

.slider {
    width: 100%;
    height: 100%;
    position: relative;
}

.slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity var(--transition-slow);
    display: flex;
    background-color: var(--color-background-light);
}

.slide.active {
    opacity: 1;
    z-index: 1;
}

.slide-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 0 var(--spacing-xxl);
    z-index: 2;
}

.slide-content h2 {
    font-size: var(--font-size-xxxl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-md);
    color: var(--color-text);
}

.slide-content p {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-lg);
    color: var(--color-text-secondary);
    max-width: 500px;
}

.slide-image {
    flex: 1;
    background-size: cover;
    background-position: center;
    position: relative;
}

.slide-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, var(--color-background-light) 0%, rgba(18, 18, 18, 0.7) 50%, rgba(18, 18, 18, 0.4) 100%);
}

.slider-controls {
    position: absolute;
    bottom: var(--spacing-lg);
    left: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
}

.prev-btn,
.next-btn {
    background-color: rgba(0, 0, 0, 0.5);
    color: var(--color-text);
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius-circle);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-fast);
    margin: 0 var(--spacing-md);
}

.prev-btn:hover,
.next-btn:hover {
    background-color: var(--color-primary);
}

.slider-dots {
    display: flex;
    gap: var(--spacing-sm);
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: var(--border-radius-circle);
    background-color: rgba(255, 255, 255, 0.3);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.dot.active {
    background-color: var(--color-primary);
    transform: scale(1.2);
}

/* Features section */
.features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    padding: 0 var(--spacing-lg);
    margin-bottom: var(--spacing-xxl);
}

.feature-card {
    background-color: var(--color-background-light);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-xl);
    text-align: center;
    transition: transform var(--transition-normal);
    box-shadow: var(--shadow-sm);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-md);
}

.feature-icon {
    width: 70px;
    height: 70px;
    background-color: rgba(29, 185, 84, 0.1);
    border-radius: var(--border-radius-circle);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
}

.feature-icon i {
    font-size: var(--font-size-xl);
    color: var(--color-primary);
}

.feature-card h3 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-md);
}

.feature-card p {
    color: var(--color-text-secondary);
    margin-bottom: var(--spacing-lg);
    min-height: 4.5rem;
}

/* Media Queries */
@media screen and (max-width: 992px) {
    .slider-container {
        height: 400px;
    }
    
    .slide-content h2 {
        font-size: var(--font-size-xxl);
    }
    
    .slide-content p {
        font-size: var(--font-size-md);
    }
}

@media screen and (max-width: 768px) {
    .slider-container {
        height: 350px;
    }

    .slide {
        flex-direction: column;
        background-size: cover;
        background-position: center;
        position: relative;
    }

    .slide::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(0deg, rgba(18, 18, 18, 0.8) 0%, rgba(18, 18, 18, 0.6) 50%, rgba(18, 18, 18, 0.4) 100%);
        z-index: 1;
    }

    .slide-content {
        padding: var(--spacing-lg);
        text-align: center;
        align-items: center;
        z-index: 2;
        position: relative;
    }

    .slide-image {
        display: none;
    }

    .features {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media screen and (max-width: 480px) {
    .slider-container {
        height: 300px;
    }
    
    .slide-content h2 {
        font-size: var(--font-size-xl);
    }
    
    .slide-content p {
        font-size: var(--font-size-sm);
    }
    
    .features {
        grid-template-columns: 1fr;
    }
}
