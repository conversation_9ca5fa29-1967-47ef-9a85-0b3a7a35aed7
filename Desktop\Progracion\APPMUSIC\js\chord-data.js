// Servicio de datos de acordes con JSONs separados por instrumento
// Este archivo maneja la carga de acordes desde archivos JSON específicos

// Datos de acordes por instrumento (carga bajo demanda)
let chordDataByInstrument = {
    guitar: null,
    piano: null,
    bass: null
};

let chordImagesByInstrument = {
    guitar: null,
    piano: null,
    bass: null
};
let chordTheory = null;
let loadingPromises = {};
let imageLoadingPromises = {};

// Función para cargar los datos de acordes de un instrumento específico
async function loadChordDataForInstrument(instrument) {
    // Si ya estamos cargando este instrumento, esperar a que termine
    if (loadingPromises[instrument]) {
        return loadingPromises[instrument];
    }

    // Si ya están cargados, devolver true
    if (chordDataByInstrument[instrument]) {
        return true;
    }

    // Crear promesa de carga para este instrumento
    loadingPromises[instrument] = (async () => {
        try {
            // Determinar la ruta correcta basándose en la ubicación actual
            const isInPagesFolder = window.location.pathname.includes('/pages/');
            const basePath = isInPagesFolder ? '../' : './';
            const fileName = `${basePath}data/acordes-${instrument}.json`;

            console.log(`Intentando cargar: ${fileName}`);
            const response = await fetch(fileName);

            if (!response.ok) {
                throw new Error(`Error al cargar ${fileName}: ${response.status}`);
            }

            const data = await response.json();
            chordDataByInstrument[instrument] = data.chords;

            console.log(`Datos de acordes para ${instrument} cargados correctamente`);
            return true;
        } catch (error) {
            console.error(`Error al cargar los datos de acordes para ${instrument}:`, error);
            loadingPromises[instrument] = null; // Resetear para permitir reintentos
            return false;
        }
    })();

    return loadingPromises[instrument];
}

// Función para cargar las imágenes de acordes de un instrumento específico
async function loadChordImagesForInstrument(instrument) {
    // Si ya estamos cargando este instrumento, esperar a que termine
    if (imageLoadingPromises[instrument]) {
        return imageLoadingPromises[instrument];
    }

    // Si ya tenemos los datos, no cargar de nuevo
    if (chordImagesByInstrument[instrument]) {
        return true;
    }

    // Crear promesa de carga para este instrumento
    imageLoadingPromises[instrument] = (async () => {
        try {
            // Determinar la ruta correcta basándose en la ubicación actual
            const isInPagesFolder = window.location.pathname.includes('/pages/');
            const basePath = isInPagesFolder ? '../' : './';

            // Mapear nombres de instrumentos a nombres de archivos
            const instrumentFileMap = {
                'guitar': 'guitarra',
                'piano': 'piano',
                'bass': 'bajo'
            };

            const fileName = `${basePath}data/images-${instrumentFileMap[instrument]}.json`;

            console.log(`Intentando cargar imágenes de ${instrument}: ${fileName}`);
            const response = await fetch(fileName);

            if (!response.ok) {
                throw new Error(`Error al cargar ${fileName}: ${response.status}`);
            }

            chordImagesByInstrument[instrument] = await response.json();
            console.log(`Imágenes de ${instrument} cargadas correctamente`);
            return true;
        } catch (error) {
            console.error(`Error al cargar las imágenes de ${instrument}:`, error);
            return false;
        }
    })();

    return imageLoadingPromises[instrument];
}

// Función para cargar todos los instrumentos
async function loadAllChordData() {
    try {
        await Promise.all([
            loadChordDataForInstrument('guitar'),
            loadChordDataForInstrument('piano'),
            loadChordDataForInstrument('bass'),
            loadChordImagesForInstrument('guitar'),
            loadChordImagesForInstrument('piano'),
            loadChordImagesForInstrument('bass')
        ]);

        // Cargar datos de teoría básicos
        if (!chordTheory) {
            chordTheory = {
                chordTypes: {
                    major: { theory: 'Acorde mayor (1-3-5)', feeling: 'Alegre, estable' },
                    minor: { theory: 'Acorde menor (1-b3-5)', feeling: 'Melancólico, triste' },
                    '7': { theory: 'Acorde dominante (1-3-5-b7)', feeling: 'Tensión, blues' }
                },
                scales: {
                    major: { name: 'Mayor', pattern: 'T-T-S-T-T-T-S' },
                    minor: { name: 'Menor', pattern: 'T-S-T-T-S-T-T' }
                }
            };
        }

        return true;
    } catch (error) {
        console.error('Error al cargar todos los datos de acordes:', error);
        return false;
    }
}

// Función para cargar los favoritos desde localStorage
function loadFavorites() {
    try {
        const favoritesJson = localStorage.getItem('musicAppFavorites');
        return favoritesJson ? JSON.parse(favoritesJson) : [];
    } catch (error) {
        console.error('Error al cargar favoritos:', error);
        return [];
    }
}

// Función para guardar favoritos en localStorage
function saveFavorites(favorites) {
    try {
        localStorage.setItem('musicAppFavorites', JSON.stringify(favorites));
        return true;
    } catch (error) {
        console.error('Error al guardar favoritos:', error);
        return false;
    }
}

// Servicio de datos de acordes simplificado
const chordDataService = {
    // Inicializar el servicio de forma lazy
    init: async function() {
        // Cargar favoritos inmediatamente (es rápido)
        this.favorites = loadFavorites();

        // Cargar todos los datos de acordes al inicio
        await loadAllChordData();

        return true;
    },

    // Lista de favoritos
    favorites: [],

    // Obtener imagen de un acorde - usando API híbrida
    getChordImage: async function(instrument, root, type = 'major') {
        try {
            // Usar el nuevo servicio de API como fuente principal
            if (window.ChordAPIService) {
                const apiResult = await window.ChordAPIService.getChordImage(instrument, root, type);
                if (apiResult.success) {
                    console.log(`✅ Acorde obtenido desde API: ${root} ${type} para ${instrument} (${apiResult.data.source})`);
                    return apiResult;
                }
            }

            // Fallback: usar sistema anterior (JSONs locales)
            console.log(`⚠️ API no disponible, usando sistema de respaldo para: ${root} ${type} ${instrument}`);
            return await this.getChordImageFromLocal(instrument, root, type);

        } catch (error) {
            console.error('Error obteniendo imagen de acorde:', error);
            return await this.getChordImageFromLocal(instrument, root, type);
        }
    },

    // Sistema de respaldo usando JSONs locales
    getChordImageFromLocal: async function(instrument, root, type = 'major') {
        // Asegurar que las imágenes del instrumento estén cargadas
        await loadChordImagesForInstrument(instrument);

        // Verificar si tenemos datos para este acorde
        const instrumentData = chordDataByInstrument[instrument];
        const instrumentImages = chordImagesByInstrument[instrument];
        const normalizedType = this.normalizeChordType(type);

        let chordInfo = null;
        let imageInfo = null;

        // Obtener información del acorde desde el JSON principal
        if (instrumentData && instrumentData[root] && instrumentData[root][normalizedType]) {
            chordInfo = instrumentData[root][normalizedType];
        }

        // Obtener imagen desde el JSON de imágenes del instrumento
        if (instrumentImages && instrumentImages[root] && instrumentImages[root][normalizedType]) {
            imageInfo = instrumentImages[root][normalizedType];
        }

        // Si tenemos información del acorde
        if (chordInfo) {
            console.log(`Acorde encontrado en JSON local: ${root} ${type} para ${instrument}`);

            return {
                success: true,
                data: {
                    imageUrl: imageInfo ? imageInfo.imageUrl : chordInfo.imageUrl,
                    altText: imageInfo ? imageInfo.altText : chordInfo.altText,
                    name: chordInfo.name,
                    positions: chordInfo.positions || [],
                    keys: chordInfo.keys || [],
                    notes: chordInfo.notes || [],
                    source: imageInfo ? imageInfo.source + ' (respaldo)' : 'JSON local (respaldo)'
                }
            };
        }

        // Si solo tenemos imagen pero no datos del acorde
        if (imageInfo) {
            console.log(`Imagen encontrada en JSON local: ${root} ${type} para ${instrument}`);

            return {
                success: true,
                data: {
                    imageUrl: imageInfo.imageUrl,
                    altText: imageInfo.altText,
                    name: `${root} ${this.getChordTypeName(type)}`,
                    positions: [],
                    keys: [],
                    notes: [],
                    source: imageInfo.source + ' (respaldo)'
                }
            };
        }

        console.log(`Acorde no encontrado en JSON local: ${root} ${type} para ${instrument}, generando SVG`);

        // Fallback final: crear SVG simple
        const imageUrl = this.createSimpleSVG(root, type, instrument);

        return {
            success: true,
            data: {
                imageUrl: imageUrl,
                altText: `${root} ${type} en ${instrument}`,
                name: `${root} ${type}`,
                positions: [],
                source: 'SVG generado (respaldo)'
            }
        };
    },

    // Crear SVG simple como fallback
    createSimpleSVG: function(root, type, instrument) {
        const svg = `
            <svg width="250" height="150" xmlns="http://www.w3.org/2000/svg">
                <rect width="250" height="150" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>
                <text x="125" y="70" text-anchor="middle" font-family="Arial" font-size="20" font-weight="bold" fill="#495057">${root} ${type}</text>
                <text x="125" y="95" text-anchor="middle" font-family="Arial" font-size="14" fill="#6c757d">${instrument}</text>
                <text x="125" y="115" text-anchor="middle" font-family="Arial" font-size="12" fill="#868e96">Diagrama no disponible</text>
            </svg>
        `;

        return `data:image/svg+xml;base64,${btoa(svg)}`;
    },

    // Obtener información teórica de un acorde - simplificado
    getChordTheory: function(type = 'major') {
        // Cargar datos si no están disponibles
        if (!chordTheory) {
            loadChordData(); // Carga asíncrona, pero devolvemos datos básicos
        }

        const normalizedType = this.normalizeChordType(type);

        // Datos básicos siempre disponibles
        const basicTheory = {
            major: { theory: 'Acorde mayor (1-3-5)', feeling: 'Alegre, estable' },
            minor: { theory: 'Acorde menor (1-b3-5)', feeling: 'Melancólico, triste' },
            '7': { theory: 'Acorde dominante (1-3-5-b7)', feeling: 'Tensión, blues' }
        };

        const theory = (chordTheory && chordTheory.chordTypes && chordTheory.chordTypes[normalizedType])
                      || basicTheory[normalizedType]
                      || { theory: 'Información no disponible', feeling: 'Desconocido' };

        return {
            success: true,
            data: theory
        };
    },

    // Obtener información de escala - simplificado
    getScaleInfo: function(scale = 'major') {
        // Datos básicos siempre disponibles
        const basicScales = {
            major: { name: 'Mayor', pattern: 'T-T-S-T-T-T-S' },
            minor: { name: 'Menor', pattern: 'T-S-T-T-S-T-T' }
        };

        const scaleInfo = (chordTheory && chordTheory.scales && chordTheory.scales[scale])
                         || basicScales[scale]
                         || { name: 'Desconocida', pattern: 'No disponible' };

        return {
            success: true,
            data: scaleInfo
        };
    },

    // Normalizar el tipo de acorde
    normalizeChordType: function(type) {
        const typeMap = {
            'major': 'major',
            'minor': 'minor',
            'm': 'minor',
            'dim': 'dim',
            'diminished': 'dim',
            'aug': 'aug',
            'augmented': 'aug',
            '7': '7',
            'dominant7': '7',
            'maj7': 'maj7',
            'major7': 'maj7',
            'min7': 'min7',
            'minor7': 'min7',
            'm7': 'min7',
            'sus2': 'sus2',
            'sus4': 'sus4',
            '9': '9',
            'add9': 'add9'
        };

        return typeMap[type.toLowerCase()] || type;
    },

    // Obtener nombre legible del tipo de acorde
    getChordTypeName: function(type) {
        const typeNames = {
            'major': 'Mayor',
            'minor': 'Menor',
            '7': '7',
            'maj7': 'maj7',
            'min7': 'min7',
            'dim': 'Disminuido',
            'aug': 'Aumentado',
            'sus2': 'sus2',
            'sus4': 'sus4',
            '9': '9',
            'add9': 'add9'
        };

        return typeNames[type] || type;
    },

    // Buscar acordes - con datos por instrumento
    searchChords: async function(query) {
        const results = [];
        const queryLower = query.toLowerCase();

        // Cargar todos los datos si no están disponibles
        await loadAllChordData();

        // Buscar en todos los instrumentos
        for (const instrument of ['guitar', 'piano', 'bass']) {
            const instrumentData = chordDataByInstrument[instrument];

            if (instrumentData) {
                for (const root in instrumentData) {
                    for (const type in instrumentData[root]) {
                        const chordInfo = instrumentData[root][type];
                        const chordName = chordInfo.name || `${root} ${type}`;

                        if (chordName.toLowerCase().includes(queryLower) ||
                            root.toLowerCase().includes(queryLower) ||
                            type.toLowerCase().includes(queryLower)) {

                            const chordId = `${instrument}-${root}-${type}`;
                            results.push({
                                id: chordId,
                                instrument,
                                root,
                                type,
                                name: chordName,
                                imageUrl: chordInfo.imageUrl,
                                isFavorite: this.isFavorite(chordId)
                            });
                        }
                    }
                }
            }
        }

        return {
            success: true,
            count: results.length,
            data: results
        };
    },

    // Verificar si un acorde es favorito
    isFavorite: function(chordId) {
        return this.favorites.includes(chordId);
    },

    // Añadir un acorde a favoritos
    addFavorite: function(instrument, root, type) {
        const chordId = `${instrument}-${root}-${type}`;

        if (!this.isFavorite(chordId)) {
            this.favorites.push(chordId);
            saveFavorites(this.favorites);
        }

        return {
            success: true,
            isFavorite: true,
            message: `Acorde ${root} ${type} añadido a favoritos`
        };
    },

    // Eliminar un acorde de favoritos
    removeFavorite: function(instrument, root, type) {
        const chordId = `${instrument}-${root}-${type}`;

        if (this.isFavorite(chordId)) {
            this.favorites = this.favorites.filter(id => id !== chordId);
            saveFavorites(this.favorites);
        }

        return {
            success: true,
            isFavorite: false,
            message: `Acorde ${root} ${type} eliminado de favoritos`
        };
    },

    // Alternar estado de favorito
    toggleFavorite: function(instrument, root, type) {
        const chordId = `${instrument}-${root}-${type}`;

        if (this.isFavorite(chordId)) {
            return this.removeFavorite(instrument, root, type);
        } else {
            return this.addFavorite(instrument, root, type);
        }
    },

    // Obtener todos los acordes favoritos - simplificado
    getFavorites: function() {
        const favorites = [];

        // Recorrer la lista de favoritos
        for (const chordId of this.favorites) {
            const [instrument, root, type] = chordId.split('-');

            // Crear objeto de favorito básico
            const favorite = {
                id: chordId,
                instrument,
                root,
                type,
                name: `${root} ${type}`,
                imageUrl: null,
                isFavorite: true
            };

            // Si tenemos datos cargados, usar la imagen real
            if (chordData &&
                chordData[instrument] &&
                chordData[instrument][root] &&
                chordData[instrument][root][type]) {
                favorite.imageUrl = chordData[instrument][root][type].imageUrl;
            }

            favorites.push(favorite);
        }

        return {
            success: true,
            count: favorites.length,
            data: favorites
        };
    }
};

// Exportar el servicio para uso en otros archivos
window.ChordDataService = chordDataService;
