// JavaScript para la página de ensayos - MusicApp

document.addEventListener('DOMContentLoaded', function() {
    // Elementos de la interfaz
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    const contentTabs = document.querySelectorAll('.content-tab');
    const contentLists = document.querySelectorAll('.content-list');
    const saveButton = document.getElementById('save-ensayo');
    
    // Verificar si el usuario está autenticado
    const isLoggedIn = MusicApp.getFromLocalStorage('musicapp_user')?.isLoggedIn || false;
    
    // Mostrar/ocultar elementos según el estado de autenticación
    const loginPrompt = document.querySelector('.login-prompt');
    if (loginPrompt) {
        loginPrompt.style.display = isLoggedIn ? 'none' : 'block';
    }
    
    // Cambiar entre pestañas principales
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tabName = this.getAttribute('data-tab');
            
            // Desactivar todas las pestañas
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // Activar la pestaña seleccionada
            this.classList.add('active');
            document.querySelector(`.${tabName}-tab`).classList.add('active');
            
            // Si cambiamos a la lista, actualizar los ensayos
            if (tabName === 'list') {
                updateEnsayosList();
            }
        });
    });
    
    // Cambiar entre pestañas de contenido
    contentTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const contentType = this.getAttribute('data-content');
            
            // Desactivar todas las pestañas de contenido
            contentTabs.forEach(t => t.classList.remove('active'));
            contentLists.forEach(list => list.classList.remove('active'));
            
            // Activar la pestaña seleccionada
            this.classList.add('active');
            document.querySelector(`.${contentType}-list`).classList.add('active');
            
            // Cargar contenido según el tipo
            loadContentList(contentType);
        });
    });
    
    // Función para cargar listas de contenido
    function loadContentList(type) {
        const listContainer = document.querySelector(`.${type}-list`);
        if (!listContainer) return;
        
        let data = [];
        let storageKey = '';
        
        switch (type) {
            case 'songs':
                // Por ahora no tenemos canciones, usar datos de ejemplo
                data = [];
                break;
            case 'chords':
                data = MusicApp.getFromLocalStorage('musicapp_cifrados') || [];
                break;
            case 'lyrics':
                data = MusicApp.getFromLocalStorage('musicapp_lyrics') || [];
                break;
        }
        
        if (data.length === 0) {
            listContainer.innerHTML = `<p class="no-content">No hay ${getContentTypeName(type)} disponibles. Crea algunos primero.</p>`;
            return;
        }
        
        // Crear lista de elementos seleccionables
        let html = '<div class="content-items">';
        data.forEach(item => {
            html += `
                <div class="content-item" data-type="${type}" data-id="${item.id}">
                    <div class="item-info">
                        <h5>${item.title || item.name}</h5>
                        <p>${item.artist || item.description || 'Sin descripción'}</p>
                    </div>
                    <button class="btn btn-sm btn-secondary add-item">
                        <i class="fas fa-plus"></i> Añadir
                    </button>
                </div>
            `;
        });
        html += '</div>';
        
        listContainer.innerHTML = html;
        
        // Añadir event listeners para los botones de añadir
        const addButtons = listContainer.querySelectorAll('.add-item');
        addButtons.forEach(button => {
            button.addEventListener('click', function() {
                const item = this.closest('.content-item');
                addItemToEnsayo(item);
            });
        });
    }
    
    // Función para obtener el nombre del tipo de contenido
    function getContentTypeName(type) {
        switch (type) {
            case 'songs': return 'canciones';
            case 'chords': return 'cifrados';
            case 'lyrics': return 'letras';
            default: return 'elementos';
        }
    }
    
    // Función para añadir elemento al ensayo
    function addItemToEnsayo(itemElement) {
        const type = itemElement.getAttribute('data-type');
        const id = itemElement.getAttribute('data-id');
        const title = itemElement.querySelector('h5').textContent;
        
        // Obtener contenedor de elementos seleccionados
        const selectedContainer = document.querySelector('.selected-items');
        
        // Verificar si ya está añadido
        if (selectedContainer.querySelector(`[data-id="${id}"]`)) {
            MusicApp.showAlert('Este elemento ya está añadido al ensayo', 'warning');
            return;
        }
        
        // Crear elemento seleccionado
        const selectedItem = document.createElement('div');
        selectedItem.className = 'selected-item';
        selectedItem.setAttribute('data-type', type);
        selectedItem.setAttribute('data-id', id);
        selectedItem.innerHTML = `
            <div class="selected-item-info">
                <i class="fas fa-${getContentIcon(type)}"></i>
                <span>${title}</span>
                <span class="item-type">(${getContentTypeName(type)})</span>
            </div>
            <button class="btn btn-sm btn-danger remove-item">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        // Ocultar mensaje de "no seleccionado" si existe
        const noSelected = selectedContainer.querySelector('.no-selected');
        if (noSelected) {
            noSelected.style.display = 'none';
        }
        
        selectedContainer.appendChild(selectedItem);
        
        // Añadir event listener para eliminar
        selectedItem.querySelector('.remove-item').addEventListener('click', function() {
            selectedItem.remove();
            
            // Mostrar mensaje si no hay elementos
            if (selectedContainer.children.length === 1) { // Solo queda .no-selected
                noSelected.style.display = 'block';
            }
        });
        
        MusicApp.showAlert(`${title} añadido al ensayo`, 'success');
    }
    
    // Función para obtener icono según el tipo de contenido
    function getContentIcon(type) {
        switch (type) {
            case 'songs': return 'music';
            case 'chords': return 'guitar';
            case 'lyrics': return 'microphone';
            default: return 'file';
        }
    }
    
    // Función para guardar ensayo
    if (saveButton) {
        saveButton.addEventListener('click', function() {
            if (!isLoggedIn) {
                MusicApp.showAlert('Debes iniciar sesión para crear ensayos.', 'error');
                return;
            }
            
            // Obtener datos del formulario
            const name = document.getElementById('ensayo-name').value.trim();
            const date = document.getElementById('ensayo-date').value;
            const duration = document.getElementById('ensayo-duration').value;
            const description = document.getElementById('ensayo-description').value.trim();
            const notes = document.getElementById('ensayo-notes').value.trim();
            
            // Validaciones
            if (!name) {
                MusicApp.showAlert('Por favor, ingresa un nombre para el ensayo.', 'error');
                document.getElementById('ensayo-name').focus();
                return;
            }
            
            if (!date) {
                MusicApp.showAlert('Por favor, selecciona una fecha para el ensayo.', 'error');
                document.getElementById('ensayo-date').focus();
                return;
            }
            
            // Obtener elementos seleccionados
            const selectedItems = [];
            const selectedElements = document.querySelectorAll('.selected-item');
            selectedElements.forEach(element => {
                selectedItems.push({
                    type: element.getAttribute('data-type'),
                    id: element.getAttribute('data-id'),
                    title: element.querySelector('span').textContent
                });
            });
            
            // Simular guardado
            MusicApp.simulateLoading(this, function() {
                // Crear objeto de ensayo
                const ensayo = {
                    id: MusicApp.generateId(),
                    name: name,
                    date: date,
                    duration: parseInt(duration) || 60,
                    description: description,
                    notes: notes,
                    items: selectedItems,
                    status: 'planned',
                    rating: 0,
                    createdAt: new Date().toISOString()
                };
                
                // Obtener ensayos existentes
                const allEnsayos = MusicApp.getFromLocalStorage('musicapp_ensayos') || [];
                
                // Añadir nuevo ensayo
                allEnsayos.push(ensayo);
                
                // Guardar en localStorage
                MusicApp.saveToLocalStorage('musicapp_ensayos', allEnsayos);
                
                // Mostrar mensaje de éxito
                MusicApp.showAlert('Ensayo creado correctamente.', 'success');
                
                // Limpiar formulario
                clearForm();
                
                // Cambiar a la pestaña de lista
                document.querySelector('.tab-btn[data-tab="list"]').click();
            });
        });
    }
    
    // Función para limpiar el formulario
    function clearForm() {
        document.getElementById('ensayo-name').value = '';
        document.getElementById('ensayo-date').value = '';
        document.getElementById('ensayo-duration').value = '';
        document.getElementById('ensayo-description').value = '';
        document.getElementById('ensayo-notes').value = '';
        
        // Limpiar elementos seleccionados
        const selectedContainer = document.querySelector('.selected-items');
        const selectedItems = selectedContainer.querySelectorAll('.selected-item');
        selectedItems.forEach(item => item.remove());
        
        const noSelected = selectedContainer.querySelector('.no-selected');
        if (noSelected) {
            noSelected.style.display = 'block';
        }
    }
    
    // Función para actualizar la lista de ensayos
    function updateEnsayosList() {
        if (!isLoggedIn) return;
        
        const ensayosGrid = document.querySelector('.ensayos-grid');
        const ensayosEmpty = document.querySelector('.ensayos-empty');
        
        if (!ensayosGrid) return;
        
        // Obtener ensayos guardados
        const allEnsayos = MusicApp.getFromLocalStorage('musicapp_ensayos') || [];
        
        if (allEnsayos.length === 0) {
            ensayosEmpty.style.display = 'block';
            ensayosGrid.innerHTML = '';
            return;
        }
        
        ensayosEmpty.style.display = 'none';
        
        // Crear tarjetas de ensayos
        let html = '';
        allEnsayos.forEach(ensayo => {
            const statusClass = `status-${ensayo.status}`;
            const statusText = getStatusText(ensayo.status);
            const stars = generateStars(ensayo.rating || 0);
            
            html += `
                <div class="ensayo-card" data-id="${ensayo.id}">
                    <div class="ensayo-header">
                        <h3 class="ensayo-title">${ensayo.name}</h3>
                        <div class="ensayo-rating">${stars}</div>
                    </div>
                    <div class="ensayo-meta">
                        <span><i class="fas fa-calendar"></i> ${MusicApp.formatDate(ensayo.date)}</span>
                        <span><i class="fas fa-clock"></i> ${ensayo.duration} min</span>
                    </div>
                    <div class="ensayo-description">${ensayo.description || 'Sin descripción'}</div>
                    <div class="ensayo-content-summary">
                        <span class="content-count">
                            <i class="fas fa-list"></i>
                            ${ensayo.items.length} elementos
                        </span>
                    </div>
                    <div class="ensayo-actions">
                        <span class="ensayo-status ${statusClass}">${statusText}</span>
                        <div class="ensayo-buttons">
                            <button class="btn btn-sm btn-secondary view-ensayo" data-id="${ensayo.id}">
                                <i class="fas fa-eye"></i> Ver
                            </button>
                            <button class="btn btn-sm btn-secondary edit-ensayo" data-id="${ensayo.id}">
                                <i class="fas fa-edit"></i> Editar
                            </button>
                            <button class="btn btn-sm btn-danger delete-ensayo" data-id="${ensayo.id}">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
        });
        
        ensayosGrid.innerHTML = html;
        
        // Añadir event listeners
        addEnsayoEventListeners();
    }
    
    // Función para obtener texto del estado
    function getStatusText(status) {
        switch (status) {
            case 'planned': return 'Planificado';
            case 'in-progress': return 'En progreso';
            case 'completed': return 'Completado';
            default: return 'Desconocido';
        }
    }
    
    // Función para generar estrellas de valoración
    function generateStars(rating) {
        let stars = '';
        for (let i = 1; i <= 5; i++) {
            const filled = i <= rating ? 'filled' : '';
            stars += `<i class="fas fa-star star ${filled}"></i>`;
        }
        return stars;
    }
    
    // Función para añadir event listeners a los ensayos
    function addEnsayoEventListeners() {
        // Botones de eliminar
        const deleteButtons = document.querySelectorAll('.delete-ensayo');
        deleteButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.stopPropagation();
                const id = this.getAttribute('data-id');
                
                if (confirm('¿Estás seguro de que deseas eliminar este ensayo?')) {
                    const allEnsayos = MusicApp.getFromLocalStorage('musicapp_ensayos') || [];
                    const updatedEnsayos = allEnsayos.filter(e => e.id !== id);
                    MusicApp.saveToLocalStorage('musicapp_ensayos', updatedEnsayos);
                    updateEnsayosList();
                    MusicApp.showAlert('Ensayo eliminado correctamente.', 'success');
                }
            });
        });
        
        // Botones de ver y editar (por ahora solo muestran mensaje)
        const viewButtons = document.querySelectorAll('.view-ensayo');
        const editButtons = document.querySelectorAll('.edit-ensayo');
        
        viewButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.stopPropagation();
                MusicApp.showAlert('Funcionalidad de visualización en desarrollo.', 'info');
            });
        });
        
        editButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.stopPropagation();
                MusicApp.showAlert('Funcionalidad de edición en desarrollo.', 'info');
            });
        });
    }
    
    // Inicializar la aplicación
    if (isLoggedIn) {
        updateEnsayosList();
        loadContentList('songs'); // Cargar contenido inicial
    }
    
    // Botón para crear primer ensayo desde estado vacío
    const createFirstButton = document.querySelector('.empty-actions .btn');
    if (createFirstButton) {
        createFirstButton.addEventListener('click', function() {
            document.querySelector('.tab-btn[data-tab="create"]').click();
        });
    }
});
